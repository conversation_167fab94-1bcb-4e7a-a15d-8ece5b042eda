# Test version comparison
Write-Host "Testing version comparison..."

# Get API response
$api = Invoke-RestMethod 'https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer'
$apiVersion = $api.version
$apiClientVersionUpload = $api.clientVersionUpload

Write-Host "API version: $apiVersion"
Write-Host "API clientVersionUpload: $apiClientVersionUpload"

# Get saved version
$settings = Get-Content "Data\settings.json" -Raw | ConvertFrom-Json
$savedVersion = $settings.roblox.version

Write-Host "Saved version: $savedVersion"

# Compare
if ($savedVersion -eq $apiVersion) {
    Write-Host "SUCCESS: Saved version matches API version - no update needed"
} else {
    Write-Host "PROBLEM: Saved version does not match API version"
    Write-Host "  Saved: $savedVersion"
    Write-Host "  API:   $apiVersion"
}

if ($savedVersion -eq $apiClientVersionUpload) {
    Write-Host "INFO: Saved version matches clientVersionUpload"
} else {
    Write-Host "INFO: Saved version does not match clientVersionUpload (this is expected)"
}

Write-Host ""
Write-Host "Now the system should use 'version' field ($apiVersion) instead of 'clientVersionUpload' field ($apiClientVersionUpload)"
