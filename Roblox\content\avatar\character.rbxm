<roblox xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.roblox.com/roblox.xsd" version="4">
	<External>null</External>
	<External>nil</External>
	<Item class="Model" referent="RBX0">
		<Properties>
			<CoordinateFrame name="ModelInPrimary">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
				<R00>1</R00>
				<R01>0</R01>
				<R02>0</R02>
				<R10>0</R10>
				<R11>1</R11>
				<R12>0</R12>
				<R20>0</R20>
				<R21>0</R21>
				<R22>1</R22>
			</CoordinateFrame>
			<string name="Name">erik.cassel</string>
			<Ref name="PrimaryPart">RBX1</Ref>
		</Properties>
		<Item class="Part" referent="RBX1">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>19.5</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Head</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>1</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="SpecialMesh" referent="RBX2">
				<Properties>
					<token name="LODX">2</token>
					<token name="LODY">2</token>
					<Content name="MeshId"><null></null></Content>
					<token name="MeshType">0</token>
					<string name="Name">Mesh</string>
					<Vector3 name="Offset">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
					</Vector3>
					<Vector3 name="Scale">
						<X>1.25</X>
						<Y>1.25</Y>
						<Z>1.25</Z>
					</Vector3>
					<Content name="TextureId"><null></null></Content>
					<Vector3 name="VertexColor">
						<X>1</X>
						<Y>1</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
			<Item class="Decal" referent="RBX3">
				<Properties>
					<token name="Face">5</token>
					<string name="Name">face</string>
					<Content name="Texture"><url>rbxasset://textures/face.png</url></Content>
					<float name="Transparency">0</float>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX94E86BA569B64785B5D8F3F9C1C9D4F8">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.600000024</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HairAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX378A74DCC4AA4A2598AA40FF503FD481">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.600000024</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HatAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX99DFBABC62D2465A8C279BF27A84727A">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>-0.600000024</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceFrontAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX1D688DF966C84BC7B8EA35A8C51B3A3E">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceCenterAttachment</string>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX4">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>18</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>-0</R02>
					<R10>-0</R10>
					<R11>1</R11>
					<R12>-0</R12>
					<R20>-0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">0</float>
				<float name="LeftParamB">0</float>
				<token name="LeftSurface">2</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Torso</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">0</float>
				<float name="RightParamB">0</float>
				<token name="RightSurface">2</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Decal" referent="RBX5">
				<Properties>
					<token name="Face">5</token>
					<string name="Name">roblox</string>
					<Content name="Texture"><null></null></Content>
					<float name="Transparency">0</float>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXF18642A6731741EB965F132B28EAF1DC">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">NeckAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXE4C1D4019C4A40DA86329D0548957CE3">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>-0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyFrontAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX9A29D0ACFCB045FE95734B1973F9F3DA">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyBackAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXB02E7106C39E485995A68DB8B3662D6A">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftCollarAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX68757F07EBFA45A8BBC77405BF28AD4A">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>1</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightCollarAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXAE8F55F8248748C0B3B008E176195348">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>-0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistFrontAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXDBB757C86A6948EE86DA6F8B9A4CBDC0">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistCenterAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX0CD5ED23664D4ECABBB224F0DAE8542E">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistBackAttachment</string>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX6">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>1.5</X>
					<Y>18</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Left Arm</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX32AAED8CBEAA432B93549F57A8CD264F">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1.0</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftShoulderAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX6E6935175E1C4EA18E576318A7CD28A7">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftGripAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX7">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>-1.5</X>
					<Y>18</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Right Arm</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX1442A14CF3BB469E8B8A84AEF3ACBA23">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1.0</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightShoulderAttachment</string>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX8300B203D1414AC48DE24AAD40EF3254">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightGripAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX8">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>0.5</X>
					<Y>16</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>-0</R02>
					<R10>-0</R10>
					<R11>1</R11>
					<R12>-0</R12>
					<R20>-0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Left Leg</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX13">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftFootAttachment</string>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX9">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>-0.5</X>
					<Y>16</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>-0</R02>
					<R10>-0</R10>
					<R11>1</R11>
					<R12>-0</R12>
					<R20>-0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Right Leg</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX14">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightFootAttachment</string>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Humanoid" referent="RBX10">
			<Properties>
				<token name="DisplayDistanceType">0</token>
				<float name="HealthDisplayDistance">100</float>
				<float name="Health_XML">100</float>
				<float name="HipHeight">0</float>
				<float name="JumpPower">50</float>
				<float name="MaxHealth">100</float>
				<float name="MaxSlopeAngle">89</float>
				<string name="Name">Humanoid</string>
				<float name="NameDisplayDistance">100</float>
				<token name="NameOcclusion">2</token>
				<float name="WalkSpeed">16</float>
			</Properties>
		</Item>
		<Item class="Part" referent="RBX11">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<int name="BrickColor">194</int>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>18</Y>
					<Z>22.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>-0</R02>
					<R10>-0</R10>
					<R11>1</R11>
					<R12>-0</R12>
					<R20>-0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="Elasticity">0.5</float>
				<float name="Friction">0.300000012</float>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">0</float>
				<float name="LeftParamB">0</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">HumanoidRootPart</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">0</float>
				<float name="RightParamB">0</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">1</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX12">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RootAttachment</string>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
	</Item>
</roblox>