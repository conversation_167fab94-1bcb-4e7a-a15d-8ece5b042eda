using Newtonsoft.Json;
using System.Diagnostics;
using System.Net.Http;
using System.Text;

namespace RobloxLauncher
{
    public partial class Form : System.Windows.Forms.Form
{
    private readonly string _baseDirectory;
    private readonly string _robloxPath;
    private readonly string _cachePath;
    private readonly string _dataPath;
    private readonly HttpClient _httpClient;
    private Settings _settings = new();
    private CacheManager? _cacheManager;
    private UpdateManager? _updateManager;

    public Form()
    {
        InitializeComponent();

        _baseDirectory = Path.GetDirectoryName(Application.ExecutablePath) ?? Environment.CurrentDirectory;
        _robloxPath = Path.Combine(_baseDirectory, "..", "Roblox");
        _cachePath = Path.Combine(_baseDirectory, "..", "Cache");
        _dataPath = Path.Combine(_baseDirectory, "..", "Data");
        _httpClient = new HttpClient();

        LoadSettings();
        InitializeUI();
        InitializeCaching();
        InitializeUpdateManager();
        CheckRobloxInstallation();
    }

    private void LoadSettings()
    {
        try
        {
            var settingsPath = Path.Combine(_dataPath, "settings.json");
            if (File.Exists(settingsPath))
            {
                var json = File.ReadAllText(settingsPath);
                _settings = JsonConvert.DeserializeObject<Settings>(json) ?? new Settings();
            }
            else
            {
                _settings = new Settings();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading settings: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
            _settings = new Settings();
        }
    }

    private void SaveSettings()
    {
        try
        {
            var settingsPath = Path.Combine(_dataPath, "settings.json");
            Directory.CreateDirectory(_dataPath);
            var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
            File.WriteAllText(settingsPath, json);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error saving settings: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }
    }

    private void InitializeUI()
    {
        this.Text = "Roblox Portable Launcher v1.0";
        this.Size = new Size(600, 400);
        this.StartPosition = FormStartPosition.CenterScreen;
        this.FormBorderStyle = FormBorderStyle.FixedSingle;
        this.MaximizeBox = false;
    }

    private void InitializeCaching()
    {
        try
        {
            if (_settings.Cache.Enabled)
            {
                var cacheConfigPath = Path.Combine(_dataPath, "cache-config.json");
                _cacheManager = new CacheManager(_cachePath, cacheConfigPath);

                // Initialize simple file monitoring for auto-caching
                StartSimpleFileCaching();

                LogMessage("Simple auto-caching enabled - monitoring Roblox temp files!");

                LogMessage("Caching system initialized successfully.");
                UpdateCacheDisplay();
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Failed to initialize caching: {ex.Message}");
        }
    }

    private void InitializeUpdateManager()
    {
        try
        {
            _updateManager = new UpdateManager(_robloxPath, _cachePath, _settings);
            LogMessage("Update manager initialized successfully.");
        }
        catch (Exception ex)
        {
            LogMessage($"Failed to initialize update manager: {ex.Message}");
        }
    }

    private async void CheckRobloxInstallation()
    {
        var robloxExe = Path.Combine(_robloxPath, "RobloxPlayerBeta.exe");
        if (!File.Exists(robloxExe))
        {
            ShowInstallationDialog();
            return;
        }

        // Check for updates if enabled
        if (_updateManager != null && _settings.Roblox.AutoUpdate && _settings.Roblox.CheckOnStartup)
        {
            LogMessage("🔍 Checking for Roblox updates...");

            try
            {
                // First, let's diagnose the current state for debugging
                LogMessage("🔍 Running startup diagnosis...");
                var diagnosis = await _updateManager.DiagnoseUpdateSystemAsync();
                LogMessage("📊 Diagnosis completed - check console for details");
                Console.WriteLine(diagnosis);

                var hasUpdate = await _updateManager.CheckForUpdatesAsync();
                if (hasUpdate)
                {
                    if (_settings.Roblox.UpdateNotification)
                    {
                        // Get detailed update information
                        var updateInfo = await GetDetailedUpdateInfoAsync();

                        var result = MessageBox.Show(
                            $"تم العثور على تحديث جديد لـ Roblox!\n\n{updateInfo}\n\nهل تريد تحديث اللعبة الآن؟\n\n✅ سيتم الحفاظ على الكاش المحفوظ",
                            "تحديث متوفر",
                            MessageBoxButtons.YesNo,
                            MessageBoxIcon.Question);

                        if (result == DialogResult.Yes)
                        {
                            await PerformUpdateAsync();
                        }
                        else
                        {
                            LogMessage("❌ User declined update");

                            // If user declines and it might be a false positive, try to fix version detection
                            if (_updateManager.GetCurrentVersionPublic() == "unknown" && _updateManager.IsRobloxWorkingProperly())
                            {
                                LogMessage("🔧 Attempting to fix version detection to prevent future false positives...");
                                var isFixed = await _updateManager.FixVersionDetectionAsync();
                                if (isFixed)
                                {
                                    LogMessage("✅ Version detection fixed");
                                    SaveSettings(); // Save the updated version info
                                }
                                else
                                {
                                    LogMessage("❌ Could not fix version detection");
                                }
                            }
                        }
                    }
                    else
                    {
                        await PerformUpdateAsync();
                    }
                }
                else
                {
                    LogMessage("✅ No updates needed");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"❌ Update check failed: {ex.Message}");
            }
        }
    }

    private async Task PerformUpdateAsync()
    {
        if (_updateManager == null) return;

        try
        {
            LogMessage("🔄 Starting Roblox update...");
            lblStatus.Text = "Updating Roblox...";
            progressBar.Visible = true;
            progressBar.Style = ProgressBarStyle.Marquee;

            var progress = new Progress<string>(message =>
            {
                Invoke(() =>
                {
                    LogMessage(message);
                    lblStatus.Text = message;
                });
            });

            var success = await _updateManager.UpdateRobloxAsync(progress);

            progressBar.Visible = false;
            progressBar.Style = ProgressBarStyle.Continuous;

            if (success)
            {
                LogMessage("✅ Roblox updated successfully!");
                lblStatus.Text = "Update completed";

                // Ensure version information is properly saved after successful update
                try
                {
                    LogMessage("💾 Saving updated version information...");
                    SaveSettings(); // Save current settings

                    // Try to fix/update version detection to prevent future false positives
                    var versionFixed = await _updateManager.FixVersionDetectionAsync();
                    if (versionFixed)
                    {
                        LogMessage("✅ Version information updated");
                        SaveSettings(); // Save again with updated version
                    }
                    else
                    {
                        LogMessage("⚠️ Could not update version information");
                    }
                }
                catch (Exception ex)
                {
                    LogMessage($"⚠️ Error saving version info: {ex.Message}");
                }

                MessageBox.Show(
                    "تم تحديث Roblox بنجاح! 🎉\n\n✅ الكاش المحفوظ لم يتأثر\n🎮 يمكنك الآن تشغيل اللعبة\n💾 تم حفظ معلومات الإصدار",
                    "تحديث مكتمل",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            else
            {
                LogMessage("❌ Roblox update failed");
                lblStatus.Text = "Update failed";

                // Get detailed error information
                var errorDetails = await GetUpdateErrorDetailsAsync();

                // Also check if update was really needed
                var updateCheckDetails = await GetUpdateCheckDetailsAsync();

                MessageBox.Show(
                    $"فشل في تحديث Roblox ❌\n\n{updateCheckDetails}\n\n{errorDetails}\n\nسيتم استخدام الإصدار الحالي",
                    "فشل التحديث",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
            }

            lblStatus.Text = "Ready";
        }
        catch (Exception ex)
        {
            progressBar.Visible = false;
            LogMessage($"❌ Update error: {ex.Message}");
            lblStatus.Text = "Update error";
        }
    }

    private async Task<string> GetDetailedUpdateInfoAsync()
    {
        if (_updateManager == null) return "";

        try
        {
            var details = new StringBuilder();

            // Get version information
            var currentVersion = _updateManager.GetCurrentVersionPublic();
            var latestVersion = await _updateManager.GetLatestVersionPublicAsync();

            details.AppendLine($"📱 الإصدار الحالي: {currentVersion}");
            details.AppendLine($"🌐 الإصدار الجديد: {latestVersion}");

            // Check if Roblox is working
            if (_updateManager.IsRobloxWorkingProperly())
            {
                details.AppendLine("✅ Roblox يعمل حالياً بشكل طبيعي");

                if (currentVersion == "unknown")
                {
                    details.AppendLine("⚠️ تحذير: قد لا تحتاج للتحديث فعلاً");
                    details.AppendLine("💡 يمكنك اختيار 'No' إذا كان Roblox يعمل بشكل جيد");
                }
            }
            else
            {
                details.AppendLine("❌ Roblox لا يعمل بشكل صحيح");
                details.AppendLine("💡 التحديث مطلوب لإصلاح المشاكل");
            }

            return details.ToString();
        }
        catch (Exception ex)
        {
            return $"خطأ في الحصول على معلومات التحديث: {ex.Message}";
        }
    }

    private async Task<string> GetUpdateCheckDetailsAsync()
    {
        if (_updateManager == null) return "";

        try
        {
            var details = new StringBuilder();
            details.AppendLine("🔍 فحص التحديث:");

            // Get version information
            var currentVersion = _updateManager.GetCurrentVersionPublic();
            var latestVersion = await _updateManager.GetLatestVersionPublicAsync();

            details.AppendLine($"📱 الإصدار الحالي: {currentVersion}");
            details.AppendLine($"🌐 أحدث إصدار: {latestVersion}");

            // Check if versions are actually different
            if (currentVersion == "unknown")
            {
                details.AppendLine("⚠️ الإصدار الحالي غير معروف");

                // Check if Roblox is actually working properly
                if (_updateManager.IsRobloxWorkingProperly())
                {
                    details.AppendLine("✅ Roblox مثبت ويعمل بشكل صحيح");
                    details.AppendLine("💡 التحديث غير ضروري - يمكنك تجاهل هذه الرسالة");
                    details.AppendLine("🔧 سيتم تحديث معلومات الإصدار تلقائياً");
                }
                else
                {
                    details.AppendLine("❌ Roblox لا يعمل بشكل صحيح");
                    details.AppendLine("💡 قد تحتاج للتحديث أو إعادة التثبيت");
                }
            }
            else if (currentVersion == latestVersion)
            {
                details.AppendLine("✅ الإصدارات متطابقة - لا حاجة للتحديث");
                details.AppendLine("⚠️ قد تكون هناك مشكلة في منطق فحص التحديث");
            }
            else
            {
                details.AppendLine("🔄 تحديث حقيقي متوفر");
            }

            return details.ToString();
        }
        catch (Exception ex)
        {
            return $"خطأ في فحص التحديث: {ex.Message}";
        }
    }

    private async Task<string> GetUpdateErrorDetailsAsync()
    {
        if (_updateManager == null) return "Update manager not available";

        try
        {
            var details = new StringBuilder();
            details.AppendLine("🔍 تفاصيل الخطأ:");

            // Check if Roblox is installed
            if (!_updateManager.IsRobloxInstalled())
            {
                details.AppendLine("❌ Roblox غير مثبت");

                // Check if system Roblox exists
                var systemRobloxPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                    "Roblox", "Versions");

                if (Directory.Exists(systemRobloxPath))
                {
                    details.AppendLine("💡 يمكن النسخ من تثبيت النظام");
                }
                else
                {
                    details.AppendLine("❌ لا يوجد تثبيت نظام");
                    details.AppendLine("💡 يرجى تثبيت Roblox من الموقع الرسمي أولاً");
                }
            }
            else
            {
                // Check internet connection
                try
                {
                    using var client = new HttpClient();
                    client.Timeout = TimeSpan.FromSeconds(5);
                    await client.GetStringAsync("https://www.roblox.com");
                    details.AppendLine("✅ الاتصال بالإنترنت يعمل");
                }
                catch
                {
                    details.AppendLine("❌ مشكلة في الاتصال بالإنترنت");
                    details.AppendLine("💡 تحقق من اتصال الإنترنت");
                }

                // Check API
                try
                {
                    using var client = new HttpClient();
                    client.Timeout = TimeSpan.FromSeconds(10);
                    var response = await client.GetStringAsync("https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer");
                    details.AppendLine("✅ API Roblox يعمل");
                }
                catch (Exception ex)
                {
                    details.AppendLine("❌ مشكلة في API Roblox");
                    details.AppendLine($"💡 {ex.Message}");
                }
            }

            return details.ToString();
        }
        catch (Exception ex)
        {
            return $"خطأ في الحصول على التفاصيل: {ex.Message}";
        }
    }

    private void ShowInstallationDialog()
    {
        var result = MessageBox.Show(
            "Roblox is not installed in the portable directory.\n\n" +
            "Would you like to set it up now?",
            "Roblox Not Found",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            SetupRoblox();
        }
    }

    private async void SetupRoblox()
    {
        try
        {
            // Check if Roblox is installed on the system
            var systemRobloxPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Roblox", "Versions");

            if (Directory.Exists(systemRobloxPath))
            {
                await CopyRobloxFromSystem(systemRobloxPath);
            }
            else
            {
                await DownloadAndInstallRoblox();
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error setting up Roblox: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async Task CopyRobloxFromSystem(string systemPath)
    {
        try
        {
            var versionDirs = Directory.GetDirectories(systemPath)
                .OrderByDescending(d => Directory.GetLastWriteTime(d))
                .ToArray();

            if (versionDirs.Length == 0)
            {
                throw new Exception("No Roblox versions found in system installation");
            }

            var latestVersion = versionDirs[0];
            var robloxExe = Path.Combine(latestVersion, "RobloxPlayerBeta.exe");

            if (!File.Exists(robloxExe))
            {
                throw new Exception("RobloxPlayerBeta.exe not found in latest version");
            }

            Directory.CreateDirectory(_robloxPath);

            // Copy all files from the latest version
            await Task.Run(() =>
            {
                CopyDirectory(latestVersion, _robloxPath, true);
            });

            MessageBox.Show("Roblox has been successfully set up for portable use!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }
        catch (Exception ex)
        {
            throw new Exception($"Failed to copy Roblox from system: {ex.Message}");
        }
    }

    private Task DownloadAndInstallRoblox()
    {
        MessageBox.Show(
            "Please install Roblox normally first, then restart this launcher.\n\n" +
            "The launcher will copy the installation for portable use.",
            "Manual Installation Required",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);

        return Task.CompletedTask;
    }

    private static void CopyDirectory(string sourceDir, string destinationDir, bool recursive)
    {
        var dir = new DirectoryInfo(sourceDir);

        if (!dir.Exists)
            throw new DirectoryNotFoundException($"Source directory not found: {dir.FullName}");

        DirectoryInfo[] dirs = dir.GetDirectories();
        Directory.CreateDirectory(destinationDir);

        foreach (FileInfo file in dir.GetFiles())
        {
            string targetFilePath = Path.Combine(destinationDir, file.Name);
            file.CopyTo(targetFilePath, true);
        }

        if (recursive)
        {
            foreach (DirectoryInfo subDir in dirs)
            {
                string newDestinationDir = Path.Combine(destinationDir, subDir.Name);
                CopyDirectory(subDir.FullName, newDestinationDir, true);
            }
        }
    }

    private void btnLaunchRoblox_Click(object sender, EventArgs e)
    {
        LaunchRoblox();
    }

    private void btnSettings_Click(object sender, EventArgs e)
    {
        ShowSettingsDialog();
    }

    private void btnCacheManager_Click(object sender, EventArgs e)
    {
        ShowCacheManager();
    }

    private void btnClearCache_Click(object sender, EventArgs e)
    {
        ClearCache();
    }

    private async void btnCheckUpdate_Click(object sender, EventArgs e)
    {
        if (_updateManager == null)
        {
            MessageBox.Show("Update manager not initialized.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        try
        {
            btnCheckUpdate.Enabled = false;
            btnCheckUpdate.Text = "🔍 Checking...";
            LogMessage("🔍 Manually checking for updates...");

            var hasUpdate = await _updateManager.CheckForUpdatesAsync();

            if (hasUpdate)
            {
                // Get detailed update information before showing dialog
                var updateInfo = await GetDetailedUpdateInfoAsync();

                var result = MessageBox.Show(
                    $"تم العثور على تحديث جديد لـ Roblox! 🎉\n\n{updateInfo}\n\nهل تريد تحديث اللعبة الآن؟\n\n✅ سيتم الحفاظ على الكاش المحفوظ\n💾 سيتم إنشاء نسخة احتياطية تلقائياً",
                    "تحديث متوفر",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await PerformUpdateAsync();
                }
            }
            else
            {
                MessageBox.Show(
                    "✅ Roblox is already up to date!\n\nلا توجد تحديثات متوفرة حالياً",
                    "No Updates",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            LogMessage($"❌ Manual update check failed: {ex.Message}");
            MessageBox.Show(
                $"فشل في فحص التحديثات:\n{ex.Message}",
                "Update Check Failed",
                MessageBoxButtons.OK,
                MessageBoxIcon.Error);
        }
        finally
        {
            btnCheckUpdate.Enabled = true;
            btnCheckUpdate.Text = "🔄 Check Update";
        }
    }

    private async void LaunchRoblox()
    {
        try
        {
            var robloxExe = Path.Combine(_robloxPath, "RobloxPlayerBeta.exe");

            if (!File.Exists(robloxExe))
            {
                MessageBox.Show("Roblox is not installed. Please set it up first.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }

            LogMessage("Launching Roblox...");
            lblStatus.Text = "Launching Roblox...";

            // Set up environment variables for portable mode
            var startInfo = new ProcessStartInfo
            {
                FileName = robloxExe,
                WorkingDirectory = _robloxPath,
                UseShellExecute = false
            };

            // Add environment variables to make Roblox use our portable directories
            startInfo.EnvironmentVariables["ROBLOX_CONTENT_FOLDER"] = Path.Combine(_robloxPath, "content");
            startInfo.EnvironmentVariables["ROBLOX_USER_DATA"] = Path.Combine(_cachePath, "UserData");

            var process = Process.Start(startInfo);

            if (process != null)
            {
                LogMessage("Roblox launched successfully!");
                lblStatus.Text = "Roblox is running";

                // Monitor the process
                await Task.Run(() =>
                {
                    process.WaitForExit();
                    Invoke(() =>
                    {
                        LogMessage("Roblox has been closed.");
                        lblStatus.Text = "Ready";
                    });
                });
            }
            else
            {
                LogMessage("Failed to launch Roblox.");
                lblStatus.Text = "Launch failed";
            }
        }
        catch (Exception ex)
        {
            LogMessage($"Error launching Roblox: {ex.Message}");
            lblStatus.Text = "Error";
            MessageBox.Show($"Error launching Roblox: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async void ShowSettingsDialog()
    {
        var options = new[]
        {
            "🔍 COMPREHENSIVE DIAGNOSIS",
            "🔧 FIX VERSION DETECTION",
            "Test Update System",
            "View Version Info",
            "Force Update Check",
            $"Skip Updates When Working: {(_settings.Roblox.SkipUpdateIfWorking ? "ON" : "OFF")}",
            "Cancel"
        };

        var choice = ShowOptionsDialog("Settings & Diagnostics", "Choose an option:", options);

        switch (choice)
        {
            case 0: // Comprehensive Diagnosis
                await ShowComprehensiveDiagnosis();
                break;
            case 1: // Fix Version Detection
                await FixVersionDetection();
                break;
            case 2: // Test Update System
                await TestUpdateSystem();
                break;
            case 3: // View Version Info
                ShowVersionInfo();
                break;
            case 4: // Force Update Check
                await ForceUpdateCheck();
                break;
            case 5: // Toggle Skip Updates When Working
                ToggleSkipUpdatesWhenWorking();
                break;
        }
    }

    private int ShowOptionsDialog(string title, string message, string[] options)
    {
        var form = new Form
        {
            Text = title,
            Size = new Size(400, 300),
            StartPosition = FormStartPosition.CenterParent,
            FormBorderStyle = FormBorderStyle.FixedDialog,
            MaximizeBox = false,
            MinimizeBox = false
        };

        var label = new Label
        {
            Text = message,
            Location = new Point(20, 20),
            Size = new Size(350, 30)
        };
        form.Controls.Add(label);

        var result = -1;
        var y = 60;

        for (int i = 0; i < options.Length; i++)
        {
            var index = i;
            var button = new Button
            {
                Text = options[i],
                Location = new Point(20, y),
                Size = new Size(350, 35),
                UseVisualStyleBackColor = true
            };
            button.Click += (s, e) => { result = index; form.Close(); };
            form.Controls.Add(button);
            y += 40;
        }

        form.ShowDialog(this);
        return result;
    }

    private async Task TestUpdateSystem()
    {
        if (_updateManager == null) return;

        var progressForm = ShowProgressDialog("Testing Update System...");

        try
        {
            var testResult = await _updateManager.TestUpdateSystemAsync();
            progressForm.Close();

            var resultForm = new Form
            {
                Text = "Update System Test Results",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterParent
            };

            var textBox = new TextBox
            {
                Text = testResult,
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Dock = DockStyle.Fill,
                Font = new Font("Consolas", 9)
            };

            resultForm.Controls.Add(textBox);
            resultForm.ShowDialog(this);
        }
        catch (Exception ex)
        {
            progressForm.Close();
            MessageBox.Show($"Test failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ShowVersionInfo()
    {
        if (_updateManager == null) return;

        var info = _updateManager.GetInstalledVersionInfo();
        MessageBox.Show(info, "Version Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
    }

    private async Task ForceUpdateCheck()
    {
        if (_updateManager == null) return;

        btnCheckUpdate.Enabled = false;
        btnCheckUpdate.Text = "🔍 Checking...";

        try
        {
            var hasUpdate = await _updateManager.CheckForUpdatesAsync();

            if (hasUpdate)
            {
                var result = MessageBox.Show(
                    "تم العثور على تحديث جديد! 🎉\n\nهل تريد التحديث الآن؟",
                    "تحديث متوفر",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    await PerformUpdateAsync();
                }
            }
            else
            {
                MessageBox.Show(
                    "✅ Roblox محدث بالفعل!\n\nلا توجد تحديثات متوفرة.",
                    "لا توجد تحديثات",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"خطأ في فحص التحديثات: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
        finally
        {
            btnCheckUpdate.Enabled = true;
            btnCheckUpdate.Text = "🔄 Check Update";
        }
    }

    private async Task ShowComprehensiveDiagnosis()
    {
        if (_updateManager == null)
        {
            MessageBox.Show("Update manager not available", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        var progressForm = ShowProgressDialog("🔍 Running comprehensive diagnosis...");

        try
        {
            var diagnosis = await _updateManager.DiagnoseUpdateSystemAsync();

            progressForm.Close();

            // Show diagnosis in a scrollable form
            var diagnosisForm = new Form
            {
                Text = "🔍 Comprehensive Update System Diagnosis",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterParent,
                MaximizeBox = true,
                MinimizeBox = false,
                ShowInTaskbar = false
            };

            var textBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Both,
                Dock = DockStyle.Fill,
                Font = new Font("Consolas", 9),
                Text = diagnosis,
                ReadOnly = true,
                WordWrap = false
            };

            var buttonPanel = new Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom
            };

            var copyButton = new Button
            {
                Text = "📋 Copy to Clipboard",
                Size = new Size(150, 30),
                Location = new Point(10, 10)
            };
            copyButton.Click += (s, e) =>
            {
                Clipboard.SetText(diagnosis);
                MessageBox.Show("Diagnosis copied to clipboard!", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            };

            var closeButton = new Button
            {
                Text = "Close",
                Size = new Size(100, 30),
                Location = new Point(170, 10)
            };
            closeButton.Click += (s, e) => diagnosisForm.Close();

            buttonPanel.Controls.Add(copyButton);
            buttonPanel.Controls.Add(closeButton);
            diagnosisForm.Controls.Add(textBox);
            diagnosisForm.Controls.Add(buttonPanel);

            diagnosisForm.ShowDialog(this);
        }
        catch (Exception ex)
        {
            progressForm.Close();
            MessageBox.Show($"Diagnosis failed: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private async Task FixVersionDetection()
    {
        if (_updateManager == null)
        {
            MessageBox.Show("Update manager not available", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        var result = MessageBox.Show(
            "🔧 إصلاح كشف الإصدار\n\n" +
            "هذه الأداة ستحاول إصلاح مشكلة كشف الإصدار الحالي لـ Roblox.\n\n" +
            "ما ستفعله الأداة:\n" +
            "• قراءة الإصدار من الملف التنفيذي\n" +
            "• قراءة الإصدار من تثبيت النظام\n" +
            "• افتراض الإصدار الأحدث إذا كان Roblox يعمل\n" +
            "• حفظ الإصدار في الإعدادات\n\n" +
            "هل تريد المتابعة؟",
            "إصلاح كشف الإصدار",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result != DialogResult.Yes)
            return;

        var progressForm = ShowProgressDialog("🔧 جاري إصلاح كشف الإصدار...");

        try
        {
            var isFixed = await _updateManager.FixVersionDetectionAsync();

            progressForm.Close();

            if (isFixed)
            {
                // Save settings after fix
                SaveSettings();

                MessageBox.Show(
                    "✅ تم إصلاح كشف الإصدار بنجاح!\n\n" +
                    "تم حفظ معلومات الإصدار الجديدة.\n" +
                    "يجب أن تختفي رسائل التحديث الخاطئة الآن.",
                    "تم الإصلاح",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Information);
            }
            else
            {
                MessageBox.Show(
                    "❌ لم يتم إصلاح كشف الإصدار\n\n" +
                    "الأسباب المحتملة:\n" +
                    "• Roblox غير مثبت بشكل صحيح\n" +
                    "• ملفات Roblox تالفة\n" +
                    "• مشكلة في الوصول للملفات\n\n" +
                    "جرب إعادة تثبيت Roblox أو استخدم التشخيص الشامل.",
                    "فشل الإصلاح",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Warning);
            }
        }
        catch (Exception ex)
        {
            progressForm.Close();
            MessageBox.Show($"خطأ في إصلاح كشف الإصدار: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ToggleSkipUpdatesWhenWorking()
    {
        _settings.Roblox.SkipUpdateIfWorking = !_settings.Roblox.SkipUpdateIfWorking;

        var status = _settings.Roblox.SkipUpdateIfWorking ? "مُفعل" : "مُعطل";
        var icon = _settings.Roblox.SkipUpdateIfWorking ? "✅" : "❌";

        MessageBox.Show(
            $"{icon} تم تغيير إعداد 'تجاهل التحديثات عند العمل بشكل طبيعي'\n\n" +
            $"الحالة الحالية: {status}\n\n" +
            $"📝 الوصف:\n" +
            $"• مُفعل: لن يطلب تحديث إذا كان Roblox يعمل بشكل طبيعي\n" +
            $"• مُعطل: سيطلب تحديث حتى لو كان Roblox يعمل\n\n" +
            $"💡 يُنصح بتركه مُفعل لتجنب التحديثات غير الضرورية",
            "تم تغيير الإعداد",
            MessageBoxButtons.OK,
            MessageBoxIcon.Information);

        // Save settings
        SaveSettings();
    }

    private Form ShowProgressDialog(string message)
    {
        var form = new Form
        {
            Text = "Please Wait",
            Size = new Size(300, 120),
            StartPosition = FormStartPosition.CenterParent,
            FormBorderStyle = FormBorderStyle.FixedDialog,
            MaximizeBox = false,
            MinimizeBox = false,
            ControlBox = false
        };

        var label = new Label
        {
            Text = message,
            Location = new Point(20, 30),
            Size = new Size(250, 30),
            TextAlign = ContentAlignment.MiddleCenter
        };

        form.Controls.Add(label);
        form.Show(this);
        Application.DoEvents();

        return form;
    }

    private void ShowCacheManager()
    {
        try
        {
            var cacheSize = GetCacheSizeInMB();
            var message = $"Cache Information:\n\n" +
                         $"Total Cache Size: {cacheSize:F1} MB\n" +
                         $"Cache Location: {_cachePath}\n\n" +
                         $"Cache Categories:\n" +
                         $"- Game Assets: {GetDirectorySizeInMB(Path.Combine(_cachePath, "GameAssets")):F1} MB\n" +
                         $"- Textures: {GetDirectorySizeInMB(Path.Combine(_cachePath, "Textures")):F1} MB\n" +
                         $"- Models: {GetDirectorySizeInMB(Path.Combine(_cachePath, "Models")):F1} MB\n" +
                         $"- Scripts: {GetDirectorySizeInMB(Path.Combine(_cachePath, "Scripts")):F1} MB\n" +
                         $"- User Data: {GetDirectorySizeInMB(Path.Combine(_cachePath, "UserData")):F1} MB";

            MessageBox.Show(message, "Cache Manager", MessageBoxButtons.OK, MessageBoxIcon.Information);
            UpdateCacheDisplay();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error accessing cache information: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }

    private void ClearCache()
    {
        var result = MessageBox.Show(
            "Are you sure you want to clear the cache?\n\nThis will delete all cached game assets but preserve user data.",
            "Clear Cache",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);

        if (result == DialogResult.Yes)
        {
            try
            {
                var cacheDirs = new[] { "GameAssets", "Textures", "Models", "Scripts" };

                foreach (var dir in cacheDirs)
                {
                    var dirPath = Path.Combine(_cachePath, dir);
                    if (Directory.Exists(dirPath))
                    {
                        Directory.Delete(dirPath, true);
                        Directory.CreateDirectory(dirPath);
                    }
                }

                LogMessage("Cache cleared successfully.");
                UpdateCacheDisplay();
                MessageBox.Show("Cache has been cleared successfully.", "Success", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                LogMessage($"Error clearing cache: {ex.Message}");
                MessageBox.Show($"Error clearing cache: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    private void LogMessage(string message)
    {
        var timestamp = DateTime.Now.ToString("HH:mm:ss");
        var logEntry = $"[{timestamp}] {message}";

        if (txtLog.InvokeRequired)
        {
            txtLog.Invoke(() =>
            {
                txtLog.AppendText(logEntry + Environment.NewLine);
                txtLog.ScrollToCaret();
            });
        }
        else
        {
            txtLog.AppendText(logEntry + Environment.NewLine);
            txtLog.ScrollToCaret();
        }
    }

    private void UpdateCacheDisplay()
    {
        try
        {
            var cacheSize = GetCacheSizeInMB();
            lblCacheSize.Text = $"Cache: {cacheSize:F1} MB";
        }
        catch
        {
            lblCacheSize.Text = "Cache: Error";
        }
    }

    private double GetCacheSizeInMB()
    {
        return GetDirectorySizeInMB(_cachePath);
    }

    private double GetDirectorySizeInMB(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
            return 0;

        try
        {
            var size = Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories)
                               .Sum(file => new FileInfo(file).Length);
            return size / (1024.0 * 1024.0);
        }
        catch
        {
            return 0;
        }
    }

    private void StartSimpleFileCaching()
    {
        LogMessage("🚀 Starting aggressive auto-caching...");

        // Start multiple monitoring tasks for maximum coverage
        _ = Task.Run(MonitorRobloxFiles);
        _ = Task.Run(MonitorTempFiles);
        _ = Task.Run(MonitorUserDataFiles);

        LogMessage("✅ Auto-caching started - will save EVERYTHING!");
    }

    private async Task MonitorRobloxFiles()
    {
        var watchedFiles = new HashSet<string>();

        while (true)
        {
            try
            {
                if (_cacheManager == null)
                {
                    await Task.Delay(1000);
                    continue;
                }

                // Monitor Roblox installation folder
                if (Directory.Exists(_robloxPath))
                {
                    var allFiles = Directory.GetFiles(_robloxPath, "*", SearchOption.AllDirectories)
                        .Where(f => new FileInfo(f).Length > 0);

                    foreach (var file in allFiles)
                    {
                        var fileKey = $"{file}:{new FileInfo(file).LastWriteTime.Ticks}";
                        if (!watchedFiles.Contains(fileKey))
                        {
                            await CacheFileIfNeeded(file, "roblox");
                            watchedFiles.Add(fileKey);
                        }
                    }
                }

                await Task.Delay(2000); // Check every 2 seconds
            }
            catch (Exception ex)
            {
                LogMessage($"Roblox monitoring error: {ex.Message}");
                await Task.Delay(5000);
            }
        }
    }

    private async Task MonitorTempFiles()
    {
        var watchedFiles = new HashSet<string>();

        while (true)
        {
            try
            {
                if (_cacheManager == null)
                {
                    await Task.Delay(1000);
                    continue;
                }

                // Monitor temp folder for any new files
                var tempPath = Path.GetTempPath();
                var recentFiles = Directory.GetFiles(tempPath, "*", SearchOption.AllDirectories)
                    .Where(f => File.GetLastWriteTime(f) > DateTime.Now.AddMinutes(-5) && new FileInfo(f).Length > 0);

                foreach (var file in recentFiles)
                {
                    var fileKey = $"{file}:{new FileInfo(file).LastWriteTime.Ticks}";
                    if (!watchedFiles.Contains(fileKey))
                    {
                        await CacheFileIfNeeded(file, "temp");
                        watchedFiles.Add(fileKey);
                    }
                }

                await Task.Delay(3000); // Check every 3 seconds
            }
            catch (Exception ex)
            {
                LogMessage($"Temp monitoring error: {ex.Message}");
                await Task.Delay(5000);
            }
        }
    }

    private async Task MonitorUserDataFiles()
    {
        var watchedFiles = new HashSet<string>();

        while (true)
        {
            try
            {
                if (_cacheManager == null)
                {
                    await Task.Delay(1000);
                    continue;
                }

                // Monitor user data folders
                var userDataPaths = new[]
                {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "Roblox"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "Roblox")
                };

                foreach (var userPath in userDataPaths)
                {
                    if (!Directory.Exists(userPath)) continue;

                    var recentFiles = Directory.GetFiles(userPath, "*", SearchOption.AllDirectories)
                        .Where(f => File.GetLastWriteTime(f) > DateTime.Now.AddMinutes(-5) && new FileInfo(f).Length > 0);

                    foreach (var file in recentFiles)
                    {
                        var fileKey = $"{file}:{new FileInfo(file).LastWriteTime.Ticks}";
                        if (!watchedFiles.Contains(fileKey))
                        {
                            await CacheFileIfNeeded(file, "userdata");
                            watchedFiles.Add(fileKey);
                        }
                    }
                }

                await Task.Delay(4000); // Check every 4 seconds
            }
            catch (Exception ex)
            {
                LogMessage($"UserData monitoring error: {ex.Message}");
                await Task.Delay(5000);
            }
        }
    }

    private async Task CacheFileIfNeeded(string filePath, string source)
    {
        try
        {
            if (_cacheManager == null || !File.Exists(filePath)) return;

            var fileInfo = new FileInfo(filePath);
            if (fileInfo.Length == 0 || fileInfo.Length > 100 * 1024 * 1024) return; // Skip empty or very large files

            var data = await File.ReadAllBytesAsync(filePath);
            var fileName = Path.GetFileName(filePath);
            var url = $"{source}://{fileName}";

            await _cacheManager.AutoCacheAssetAsync(url, data);
            LogMessage($"💾 Cached: {fileName} ({data.Length} bytes) from {source}");
        }
        catch
        {
            // Ignore individual file errors
        }
    }

    private bool IsRobloxRelatedFile(string filePath)
    {
        var fileName = Path.GetFileName(filePath).ToLower();
        var extension = Path.GetExtension(filePath).ToLower();

        return fileName.Contains("roblox") ||
               extension == ".rbxm" || extension == ".rbxl" ||
               extension == ".mesh" || extension == ".png" ||
               extension == ".jpg" || extension == ".ogg" ||
               extension == ".mp3" || extension == ".wav";
    }

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            _httpClient?.Dispose();
            _cacheManager?.Dispose();
            _updateManager?.Dispose();
            components?.Dispose();
        }
        base.Dispose(disposing);
    }
    }
}
