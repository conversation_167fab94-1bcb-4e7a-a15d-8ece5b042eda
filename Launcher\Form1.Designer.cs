﻿namespace RobloxLauncher
{
    partial class Form
{
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    #region Windows Form Designer generated code

    /// <summary>
    ///  Required method for Designer support - do not modify
    ///  the contents of this method with the code editor.
    /// </summary>
    private void InitializeComponent()
    {
        this.btnLaunchRoblox = new System.Windows.Forms.Button();
        this.btnSettings = new System.Windows.Forms.Button();
        this.btnCacheManager = new System.Windows.Forms.Button();
        this.btnCheckUpdate = new System.Windows.Forms.Button();
        this.lblStatus = new System.Windows.Forms.Label();
        this.progressBar = new System.Windows.Forms.ProgressBar();
        this.txtLog = new System.Windows.Forms.TextBox();
        this.lblCacheSize = new System.Windows.Forms.Label();
        this.btnClearCache = new System.Windows.Forms.Button();
        this.SuspendLayout();
        //
        // btnLaunchRoblox
        //
        this.btnLaunchRoblox.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Bold);
        this.btnLaunchRoblox.Location = new System.Drawing.Point(50, 50);
        this.btnLaunchRoblox.Name = "btnLaunchRoblox";
        this.btnLaunchRoblox.Size = new System.Drawing.Size(200, 50);
        this.btnLaunchRoblox.TabIndex = 0;
        this.btnLaunchRoblox.Text = "Launch Roblox";
        this.btnLaunchRoblox.UseVisualStyleBackColor = true;
        this.btnLaunchRoblox.Click += new System.EventHandler(this.btnLaunchRoblox_Click);
        //
        // btnSettings
        //
        this.btnSettings.Location = new System.Drawing.Point(270, 50);
        this.btnSettings.Name = "btnSettings";
        this.btnSettings.Size = new System.Drawing.Size(100, 30);
        this.btnSettings.TabIndex = 1;
        this.btnSettings.Text = "Settings";
        this.btnSettings.UseVisualStyleBackColor = true;
        this.btnSettings.Click += new System.EventHandler(this.btnSettings_Click);
        //
        // btnCacheManager
        //
        this.btnCacheManager.Location = new System.Drawing.Point(270, 90);
        this.btnCacheManager.Name = "btnCacheManager";
        this.btnCacheManager.Size = new System.Drawing.Size(100, 30);
        this.btnCacheManager.TabIndex = 2;
        this.btnCacheManager.Text = "Cache Manager";
        this.btnCacheManager.UseVisualStyleBackColor = true;
        this.btnCacheManager.Click += new System.EventHandler(this.btnCacheManager_Click);
        //
        // btnCheckUpdate
        //
        this.btnCheckUpdate.Location = new System.Drawing.Point(450, 50);
        this.btnCheckUpdate.Name = "btnCheckUpdate";
        this.btnCheckUpdate.Size = new System.Drawing.Size(100, 30);
        this.btnCheckUpdate.TabIndex = 8;
        this.btnCheckUpdate.Text = "🔄 Check Update";
        this.btnCheckUpdate.UseVisualStyleBackColor = true;
        this.btnCheckUpdate.Click += new System.EventHandler(this.btnCheckUpdate_Click);
        //
        // lblStatus
        //
        this.lblStatus.AutoSize = true;
        this.lblStatus.Location = new System.Drawing.Point(50, 120);
        this.lblStatus.Name = "lblStatus";
        this.lblStatus.Size = new System.Drawing.Size(39, 15);
        this.lblStatus.TabIndex = 3;
        this.lblStatus.Text = "Ready";
        //
        // progressBar
        //
        this.progressBar.Location = new System.Drawing.Point(50, 150);
        this.progressBar.Name = "progressBar";
        this.progressBar.Size = new System.Drawing.Size(320, 20);
        this.progressBar.TabIndex = 4;
        this.progressBar.Visible = false;
        //
        // txtLog
        //
        this.txtLog.Location = new System.Drawing.Point(50, 190);
        this.txtLog.Multiline = true;
        this.txtLog.Name = "txtLog";
        this.txtLog.ReadOnly = true;
        this.txtLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
        this.txtLog.Size = new System.Drawing.Size(500, 150);
        this.txtLog.TabIndex = 5;
        //
        // lblCacheSize
        //
        this.lblCacheSize.AutoSize = true;
        this.lblCacheSize.Location = new System.Drawing.Point(400, 55);
        this.lblCacheSize.Name = "lblCacheSize";
        this.lblCacheSize.Size = new System.Drawing.Size(70, 15);
        this.lblCacheSize.TabIndex = 6;
        this.lblCacheSize.Text = "Cache: 0 MB";
        //
        // btnClearCache
        //
        this.btnClearCache.Location = new System.Drawing.Point(400, 90);
        this.btnClearCache.Name = "btnClearCache";
        this.btnClearCache.Size = new System.Drawing.Size(100, 30);
        this.btnClearCache.TabIndex = 7;
        this.btnClearCache.Text = "Clear Cache";
        this.btnClearCache.UseVisualStyleBackColor = true;
        this.btnClearCache.Click += new System.EventHandler(this.btnClearCache_Click);
        //
        // MainForm
        //
        this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
        this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
        this.ClientSize = new System.Drawing.Size(600, 400);
        this.Controls.Add(this.btnClearCache);
        this.Controls.Add(this.lblCacheSize);
        this.Controls.Add(this.txtLog);
        this.Controls.Add(this.progressBar);
        this.Controls.Add(this.lblStatus);
        this.Controls.Add(this.btnCheckUpdate);
        this.Controls.Add(this.btnCacheManager);
        this.Controls.Add(this.btnSettings);
        this.Controls.Add(this.btnLaunchRoblox);
        this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedSingle;
        this.MaximizeBox = false;
        this.Name = "MainForm";
        this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
        this.Text = "Roblox Portable Launcher";
        this.ResumeLayout(false);
        this.PerformLayout();
    }

    private System.Windows.Forms.Button btnLaunchRoblox;
    private System.Windows.Forms.Button btnSettings;
    private System.Windows.Forms.Button btnCacheManager;
    private System.Windows.Forms.Button btnCheckUpdate;
    private System.Windows.Forms.Label lblStatus;
    private System.Windows.Forms.ProgressBar progressBar;
    private System.Windows.Forms.TextBox txtLog;
    private System.Windows.Forms.Label lblCacheSize;
    private System.Windows.Forms.Button btnClearCache;

    #endregion
    }
}
