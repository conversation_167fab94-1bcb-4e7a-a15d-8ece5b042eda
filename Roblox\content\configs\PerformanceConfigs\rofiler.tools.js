
var ToolsModule = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir ||= __filename;
  return (
function(moduleArg = {}) {

var Module=moduleArg;var readyPromiseResolve,readyPromiseReject;Module["ready"]=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});["_malloc","_free","_Init","_memory","_ParseRaw","_SetWorkerMode","_call_js_result","_call_js_exec","_call_js_checkenv","___indirect_function_table","_ExportFlamegraphCpu","_ExportFlamegraphMem","_ExportFlamegraphComboDiff","___emscripten_embedded_file_data","onRuntimeInitialized"].forEach(prop=>{if(!Object.getOwnPropertyDescriptor(Module["ready"],prop)){Object.defineProperty(Module["ready"],prop,{get:()=>abort("You are getting "+prop+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js"),set:()=>abort("You are setting "+prop+" on the Promise object, instead of the instance. Use .then() to get called back with the instance, see the MODULARIZE docs in src/settings.js")})}});var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram="./this.program";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=typeof window=="object";var ENVIRONMENT_IS_WORKER=typeof importScripts=="function";var ENVIRONMENT_IS_NODE=typeof process=="object"&&typeof process.versions=="object"&&typeof process.versions.node=="string";var ENVIRONMENT_IS_SHELL=!ENVIRONMENT_IS_WEB&&!ENVIRONMENT_IS_NODE&&!ENVIRONMENT_IS_WORKER;if(Module["ENVIRONMENT"]){throw new Error("Module.ENVIRONMENT has been deprecated. To force the environment, use the ENVIRONMENT compile-time option (for example, -sENVIRONMENT=web or -sENVIRONMENT=node)")}var scriptDirectory="";function locateFile(path){if(Module["locateFile"]){return Module["locateFile"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_NODE){if(typeof process=="undefined"||!process.release||process.release.name!=="node")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");var nodeVersion=process.versions.node;var numericVersion=nodeVersion.split(".").slice(0,3);numericVersion=numericVersion[0]*1e4+numericVersion[1]*100+numericVersion[2].split("-")[0]*1;if(numericVersion<16e4){throw new Error("This emscripten-generated code requires node v16.0.0 (detected v"+nodeVersion+")")}var fs=require("fs");var nodePath=require("path");if(ENVIRONMENT_IS_WORKER){scriptDirectory=nodePath.dirname(scriptDirectory)+"/"}else{scriptDirectory=__dirname+"/"}read_=(filename,binary)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);return fs.readFileSync(filename,binary?undefined:"utf8")};readBinary=filename=>{var ret=read_(filename,true);if(!ret.buffer){ret=new Uint8Array(ret)}assert(ret.buffer);return ret};readAsync=(filename,onload,onerror,binary=true)=>{filename=isFileURI(filename)?new URL(filename):nodePath.normalize(filename);fs.readFile(filename,binary?undefined:"utf8",(err,data)=>{if(err)onerror(err);else onload(binary?data.buffer:data)})};if(!Module["thisProgram"]&&process.argv.length>1){thisProgram=process.argv[1].replace(/\\/g,"/")}arguments_=process.argv.slice(2);quit_=(status,toThrow)=>{process.exitCode=status;throw toThrow}}else if(ENVIRONMENT_IS_SHELL){if(typeof process=="object"&&typeof require==="function"||typeof window=="object"||typeof importScripts=="function")throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");if(typeof read!="undefined"){read_=read}readBinary=f=>{if(typeof readbuffer=="function"){return new Uint8Array(readbuffer(f))}let data=read(f,"binary");assert(typeof data=="object");return data};readAsync=(f,onload,onerror)=>{setTimeout(()=>onload(readBinary(f)))};if(typeof clearTimeout=="undefined"){globalThis.clearTimeout=id=>{}}if(typeof setTimeout=="undefined"){globalThis.setTimeout=f=>typeof f=="function"?f():abort()}if(typeof scriptArgs!="undefined"){arguments_=scriptArgs}else if(typeof arguments!="undefined"){arguments_=arguments}if(typeof quit=="function"){quit_=(status,toThrow)=>{setTimeout(()=>{if(!(toThrow instanceof ExitStatus)){let toLog=toThrow;if(toThrow&&typeof toThrow=="object"&&toThrow.stack){toLog=[toThrow,toThrow.stack]}err(`exiting due to exception: ${toLog}`)}quit(status)});throw toThrow}}if(typeof print!="undefined"){if(typeof console=="undefined")console={};console.log=print;console.warn=console.error=typeof printErr!="undefined"?printErr:print}}else if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!="undefined"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.startsWith("blob:")){scriptDirectory=""}else{scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,"").lastIndexOf("/")+1)}if(!(typeof window=="object"||typeof importScripts=="function"))throw new Error("not compiled for this environment (did you build to HTML and try to run it not on the web, or set ENVIRONMENT to something - like node - and run it someplace else - like on the web?)");{read_=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,false);xhr.responseType="arraybuffer";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open("GET",url,true);xhr.responseType="arraybuffer";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else{throw new Error("environment detection error")}var out=Module["print"]||console.log.bind(console);var err=Module["printErr"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;checkIncomingModuleAPI();if(Module["arguments"])arguments_=Module["arguments"];legacyModuleProp("arguments","arguments_");if(Module["thisProgram"])thisProgram=Module["thisProgram"];legacyModuleProp("thisProgram","thisProgram");if(Module["quit"])quit_=Module["quit"];legacyModuleProp("quit","quit_");assert(typeof Module["memoryInitializerPrefixURL"]=="undefined","Module.memoryInitializerPrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["pthreadMainPrefixURL"]=="undefined","Module.pthreadMainPrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["cdInitializerPrefixURL"]=="undefined","Module.cdInitializerPrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["filePackagePrefixURL"]=="undefined","Module.filePackagePrefixURL option was removed, use Module.locateFile instead");assert(typeof Module["read"]=="undefined","Module.read option was removed (modify read_ in JS)");assert(typeof Module["readAsync"]=="undefined","Module.readAsync option was removed (modify readAsync in JS)");assert(typeof Module["readBinary"]=="undefined","Module.readBinary option was removed (modify readBinary in JS)");assert(typeof Module["setWindowTitle"]=="undefined","Module.setWindowTitle option was removed (modify emscripten_set_window_title in JS)");assert(typeof Module["TOTAL_MEMORY"]=="undefined","Module.TOTAL_MEMORY has been renamed Module.INITIAL_MEMORY");legacyModuleProp("asm","wasmExports");legacyModuleProp("read","read_");legacyModuleProp("readAsync","readAsync");legacyModuleProp("readBinary","readBinary");legacyModuleProp("setWindowTitle","setWindowTitle");assert(!ENVIRONMENT_IS_SHELL,"shell environment detected but not enabled at build time.  Add `shell` to `-sENVIRONMENT` to enable.");var wasmBinary;if(Module["wasmBinary"])wasmBinary=Module["wasmBinary"];legacyModuleProp("wasmBinary","wasmBinary");if(typeof WebAssembly!="object"){err("no native wasm support detected")}function intArrayFromBase64(s){if(typeof ENVIRONMENT_IS_NODE!="undefined"&&ENVIRONMENT_IS_NODE){var buf=Buffer.from(s,"base64");return new Uint8Array(buf.buffer,buf.byteOffset,buf.length)}var decoded=atob(s);var bytes=new Uint8Array(decoded.length);for(var i=0;i<decoded.length;++i){bytes[i]=decoded.charCodeAt(i)}return bytes}function tryParseAsDataURI(filename){if(!isDataURI(filename)){return}return intArrayFromBase64(filename.slice(dataURIPrefix.length))}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort("Assertion failed"+(text?": "+text:""))}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module["HEAP8"]=HEAP8=new Int8Array(b);Module["HEAP16"]=HEAP16=new Int16Array(b);Module["HEAPU8"]=HEAPU8=new Uint8Array(b);Module["HEAPU16"]=HEAPU16=new Uint16Array(b);Module["HEAP32"]=HEAP32=new Int32Array(b);Module["HEAPU32"]=HEAPU32=new Uint32Array(b);Module["HEAPF32"]=HEAPF32=new Float32Array(b);Module["HEAPF64"]=HEAPF64=new Float64Array(b)}assert(!Module["STACK_SIZE"],"STACK_SIZE can no longer be set at runtime.  Use -sSTACK_SIZE at link time");assert(typeof Int32Array!="undefined"&&typeof Float64Array!=="undefined"&&Int32Array.prototype.subarray!=undefined&&Int32Array.prototype.set!=undefined,"JS engine does not provide full typed array support");assert(!Module["wasmMemory"],"Use of `wasmMemory` detected.  Use -sIMPORTED_MEMORY to define wasmMemory externally");assert(!Module["INITIAL_MEMORY"],"Detected runtime INITIAL_MEMORY setting.  Use -sIMPORTED_MEMORY to define wasmMemory dynamically");function writeStackCookie(){var max=_emscripten_stack_get_end();assert((max&3)==0);if(max==0){max+=4}HEAPU32[max>>2]=34821223;HEAPU32[max+4>>2]=2310721022;HEAPU32[0>>2]=1668509029}function checkStackCookie(){if(ABORT)return;var max=_emscripten_stack_get_end();if(max==0){max+=4}var cookie1=HEAPU32[max>>2];var cookie2=HEAPU32[max+4>>2];if(cookie1!=34821223||cookie2!=2310721022){abort(`Stack overflow! Stack cookie has been overwritten at ${ptrToString(max)}, expected hex dwords 0x89BACDFE and 0x2135467, but received ${ptrToString(cookie2)} ${ptrToString(cookie1)}`)}if(HEAPU32[0>>2]!=1668509029){abort("Runtime error: The application has corrupted its heap memory area (address zero)!")}}(function(){var h16=new Int16Array(1);var h8=new Int8Array(h16.buffer);h16[0]=25459;if(h8[0]!==115||h8[1]!==99)throw"Runtime error: expected the system to be little-endian! (Run with -sSUPPORT_BIG_ENDIAN to bypass)"})();var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module["preRun"]){if(typeof Module["preRun"]=="function")Module["preRun"]=[Module["preRun"]];while(Module["preRun"].length){addOnPreRun(Module["preRun"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){assert(!runtimeInitialized);runtimeInitialized=true;checkStackCookie();if(!Module["noFSInit"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){checkStackCookie();if(Module["postRun"]){if(typeof Module["postRun"]=="function")Module["postRun"]=[Module["postRun"]];while(Module["postRun"].length){addOnPostRun(Module["postRun"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}assert(Math.imul,"This browser does not support Math.imul(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");assert(Math.fround,"This browser does not support Math.fround(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");assert(Math.clz32,"This browser does not support Math.clz32(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");assert(Math.trunc,"This browser does not support Math.trunc(), build with LEGACY_VM_SUPPORT or POLYFILL_OLD_MATH_FUNCTIONS to add in a polyfill");var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;var runDependencyTracking={};function getUniqueRunDependency(id){var orig=id;while(1){if(!runDependencyTracking[id])return id;id=orig+Math.random()}}function addRunDependency(id){runDependencies++;Module["monitorRunDependencies"]?.(runDependencies);if(id){assert(!runDependencyTracking[id]);runDependencyTracking[id]=1;if(runDependencyWatcher===null&&typeof setInterval!="undefined"){runDependencyWatcher=setInterval(()=>{if(ABORT){clearInterval(runDependencyWatcher);runDependencyWatcher=null;return}var shown=false;for(var dep in runDependencyTracking){if(!shown){shown=true;err("still waiting on run dependencies:")}err(`dependency: ${dep}`)}if(shown){err("(end of list)")}},1e4)}}else{err("warning: run dependency added without ID")}}function removeRunDependency(id){runDependencies--;Module["monitorRunDependencies"]?.(runDependencies);if(id){assert(runDependencyTracking[id]);delete runDependencyTracking[id]}else{err("warning: run dependency removed without ID")}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){Module["onAbort"]?.(what);what="Aborted("+what+")";err(what);ABORT=true;EXITSTATUS=1;var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix="data:application/octet-stream;base64,";var isDataURI=filename=>filename.startsWith(dataURIPrefix);var isFileURI=filename=>filename.startsWith("file://");function createExportWrapper(name){return(...args)=>{assert(runtimeInitialized,`native function \`${name}\` called before runtime initialization`);var f=wasmExports[name];assert(f,`exported native function \`${name}\` not found`);return f(...args)}}var wasmBinaryFile;wasmBinaryFile="data:application/octet-stream;base64,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";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}var binary=tryParseAsDataURI(file);if(binary){return binary}if(readBinary){return readBinary(file)}throw"both async and sync fetching of the wasm failed"}function getBinaryPromise(binaryFile){return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);if(isFileURI(wasmBinaryFile)){err(`warning: Loading from a file URI (${wasmBinaryFile}) is not supported in most browsers. See https://emscripten.org/docs/getting_started/FAQ.html#how-do-i-run-a-local-webserver-for-testing-why-does-my-program-stall-in-downloading-or-preparing`)}abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={"env":wasmImports,"wasi_snapshot_preview1":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports["memory"];assert(wasmMemory,"memory not found in wasm exports");updateMemoryViews();wasmTable=wasmExports["__indirect_function_table"];assert(wasmTable,"table not found in wasm exports");addOnInit(wasmExports["__wasm_call_ctors"]);removeRunDependency("wasm-instantiate");return wasmExports}addRunDependency("wasm-instantiate");var trueModule=Module;function receiveInstantiationResult(result){assert(Module===trueModule,"the Module object should not be replaced during async compilation - perhaps the order of HTML elements is wrong?");trueModule=null;receiveInstance(result["instance"])}if(Module["instantiateWasm"]){try{return Module["instantiateWasm"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);readyPromiseReject(e)}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult).catch(readyPromiseReject);return{}}var tempDouble;var tempI64;function legacyModuleProp(prop,newName,incoming=true){if(!Object.getOwnPropertyDescriptor(Module,prop)){Object.defineProperty(Module,prop,{configurable:true,get(){let extra=incoming?" (the initial value can be provided on Module, but after startup the value is only looked for on a local variable of that name)":"";abort(`\`Module.${prop}\` has been replaced by \`${newName}\``+extra)}})}}function ignoredModuleProp(prop){if(Object.getOwnPropertyDescriptor(Module,prop)){abort(`\`Module.${prop}\` was supplied but \`${prop}\` not included in INCOMING_MODULE_JS_API`)}}function isExportedByForceFilesystem(name){return name==="FS_createPath"||name==="FS_createDataFile"||name==="FS_createPreloadedFile"||name==="FS_unlink"||name==="addRunDependency"||name==="FS_createLazyFile"||name==="FS_createDevice"||name==="removeRunDependency"}function missingGlobal(sym,msg){if(typeof globalThis!=="undefined"){Object.defineProperty(globalThis,sym,{configurable:true,get(){warnOnce(`\`${sym}\` is not longer defined by emscripten. ${msg}`);return undefined}})}}missingGlobal("buffer","Please use HEAP8.buffer or wasmMemory.buffer");missingGlobal("asm","Please use wasmExports instead");function missingLibrarySymbol(sym){if(typeof globalThis!=="undefined"&&!Object.getOwnPropertyDescriptor(globalThis,sym)){Object.defineProperty(globalThis,sym,{configurable:true,get(){var msg=`\`${sym}\` is a library symbol and not included by default; add it to your library.js __deps or to DEFAULT_LIBRARY_FUNCS_TO_INCLUDE on the command line`;var librarySymbol=sym;if(!librarySymbol.startsWith("_")){librarySymbol="$"+sym}msg+=` (e.g. -sDEFAULT_LIBRARY_FUNCS_TO_INCLUDE='${librarySymbol}')`;if(isExportedByForceFilesystem(sym)){msg+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"}warnOnce(msg);return undefined}})}unexportedRuntimeSymbol(sym)}function unexportedRuntimeSymbol(sym){if(!Object.getOwnPropertyDescriptor(Module,sym)){Object.defineProperty(Module,sym,{configurable:true,get(){var msg=`'${sym}' was not exported. add it to EXPORTED_RUNTIME_METHODS (see the Emscripten FAQ)`;if(isExportedByForceFilesystem(sym)){msg+=". Alternatively, forcing filesystem support (-sFORCE_FILESYSTEM) can export this for you"}abort(msg)}})}}function call_js_result(pData,size){if(self.isWorker){if(size>0){postMessage(UTF8ToString(pData,size))}postMessage("")}else{window.SaveExportResult(UTF8ToString(pData,size))}}function call_js_exec(pData,size){if(self.isWorker){postMessage(UTF8ToString(pData,size))}else{window.ExecStatement(UTF8ToString(pData,size))}}function call_js_checkenv(pData,size){self.isWorker=typeof WorkerGlobalScope!="undefined"&&self instanceof WorkerGlobalScope;var SetWorkerMode=Tools.cwrap("SetWorkerMode","number",["number"]);SetWorkerMode(isWorker?1:0);if(self.isWorker){console.log("Wasm module is running in worker mode");self.onmessage=function(event){var funcName=event.data.func?event.data.func:"ParseRaw";var meta=event.data.meta;var dataArr=new Uint8Array(event.data.payload);var dataPtr=Tools._malloc(dataArr.length);Tools.HEAPU8.set(dataArr,dataPtr);var func=Tools.cwrap(funcName,"number",["string","number","number"]);func(meta,dataPtr,dataArr.length);Tools._free(dataPtr)};postMessage(UTF8ToString(pData,size));postMessage("")}else{console.log("Wasm module is running in the main thread");window.SaveExportResult(UTF8ToString(pData,size))}}function ExitStatus(status){this.name="ExitStatus";this.message=`Program terminated with exit(${status})`;this.status=status}var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var noExitRuntime=Module["noExitRuntime"]||true;var ptrToString=ptr=>{assert(typeof ptr==="number");ptr>>>=0;return"0x"+ptr.toString(16).padStart(8,"0")};var warnOnce=text=>{warnOnce.shown||={};if(!warnOnce.shown[text]){warnOnce.shown[text]=1;if(ENVIRONMENT_IS_NODE)text="warning: "+text;err(text)}};class ExceptionInfo{constructor(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24}set_type(type){HEAPU32[this.ptr+4>>2]=type}get_type(){return HEAPU32[this.ptr+4>>2]}set_destructor(destructor){HEAPU32[this.ptr+8>>2]=destructor}get_destructor(){return HEAPU32[this.ptr+8>>2]}set_caught(caught){caught=caught?1:0;HEAP8[this.ptr+12]=caught}get_caught(){return HEAP8[this.ptr+12]!=0}set_rethrown(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13]=rethrown}get_rethrown(){return HEAP8[this.ptr+13]!=0}init(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)}set_adjusted_ptr(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr}get_adjusted_ptr(){return HEAPU32[this.ptr+16>>2]}get_exception_ptr(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;assert(false,"Exception thrown, but exception catching is not enabled. Compile with -sNO_DISABLE_EXCEPTION_CATCHING or -sEXCEPTION_CATCHING_ALLOWED=[..] to catch.")};var PATH={isAbs:path=>path.charAt(0)==="/",splitPath:filename=>{var splitPathRe=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last==="."){parts.splice(i,1)}else if(last===".."){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift("..")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)==="/";path=PATH.normalizeArray(path.split("/").filter(p=>!!p),!isAbsolute).join("/");if(!path&&!isAbsolute){path="."}if(path&&trailingSlash){path+="/"}return(isAbsolute?"/":"")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return"."}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path==="/")return"/";path=PATH.normalize(path);path=path.replace(/\/$/,"");var lastSlash=path.lastIndexOf("/");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:(...paths)=>PATH.normalize(paths.join("/")),join2:(l,r)=>PATH.normalize(l+"/"+r)};var initRandomFill=()=>{if(typeof crypto=="object"&&typeof crypto["getRandomValues"]=="function"){return view=>crypto.getRandomValues(view)}else if(ENVIRONMENT_IS_NODE){try{var crypto_module=require("crypto");var randomFillSync=crypto_module["randomFillSync"];if(randomFillSync){return view=>crypto_module["randomFillSync"](view)}var randomBytes=crypto_module["randomBytes"];return view=>(view.set(randomBytes(view.byteLength)),view)}catch(e){}}abort("no cryptographic support found for randomDevice. consider polyfilling it if you want to use something insecure like Math.random(), e.g. put this in a --pre-js: var crypto = { getRandomValues: (array) => { for (var i = 0; i < array.length; i++) array[i] = (Math.random()*256)|0 } };")};var randomFill=view=>(randomFill=initRandomFill())(view);var PATH_FS={resolve:(...args)=>{var resolvedPath="",resolvedAbsolute=false;for(var i=args.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?args[i]:FS.cwd();if(typeof path!="string"){throw new TypeError("Arguments to path.resolve must be strings")}else if(!path){return""}resolvedPath=path+"/"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split("/").filter(p=>!!p),!resolvedAbsolute).join("/");return(resolvedAbsolute?"/":"")+resolvedPath||"."},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!=="")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!=="")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split("/"));var toParts=trim(to.split("/"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push("..")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join("/")}};var UTF8Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf8"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str="";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{if((u0&248)!=240)warnOnce("Invalid UTF-8 leading byte "+ptrToString(u0)+" encountered when deserializing a UTF-8 string in wasm memory to a JS string!");u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var FS_stdin_getChar_buffer=[];var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{assert(typeof str==="string",`stringToUTF8Array expects a string (got ${typeof str})`);if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;if(u>1114111)warnOnce("Invalid Unicode code point "+ptrToString(u)+" encountered when serializing a JS string to a UTF-8 string in wasm memory! (Valid unicode code points should be in range 0-0x10FFFF).");heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(ENVIRONMENT_IS_NODE){var BUFSIZE=256;var buf=Buffer.alloc(BUFSIZE);var bytesRead=0;var fd=process.stdin.fd;try{bytesRead=fs.readSync(fd,buf)}catch(e){if(e.toString().includes("EOF"))bytesRead=0;else throw e}if(bytesRead>0){result=buf.slice(0,bytesRead).toString("utf-8")}else{result=null}}else if(typeof window!="undefined"&&typeof window.prompt=="function"){result=window.prompt("Input: ");if(result!==null){result+="\n"}}else if(typeof readline=="function"){result=readline();if(result!==null){result+="\n"}}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var mmapAlloc=size=>{abort("internal error: mmapAlloc called but `emscripten_builtin_memalign` native symbol not exported")};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,"/",16384|511,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}MEMFS.ops_table||={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}};var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw FS.genericErrors[44]},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir(node){var entries=[".",".."];for(var key of Object.keys(node.contents)){entries.push(key)}return entries},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);assert(size>=0);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){assert(!(buffer instanceof ArrayBuffer));if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){assert(position===0,"canOwn must imply no weird position inside the file");node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):"";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file "${url}" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file "${url}" failed.`}});if(dep)addRunDependency(dep)};var FS_createDataFile=(parent,name,fileData,canRead,canWrite,canOwn)=>{FS.createDataFile(parent,name,fileData,canRead,canWrite,canOwn)};var preloadPlugins=Module["preloadPlugins"]||[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!="undefined")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin["canHandle"](fullname)){plugin["handle"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){preFinish?.();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}onload?.();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{onerror?.();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url=="string"){asyncLoad(url,processData,onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={"r":0,"r+":2,"w":512|64|1,"w+":512|64|2,"a":1024|64|1,"a+":1024|64|2};var flags=flagModes[str];if(typeof flags=="undefined"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var ERRNO_MESSAGES={0:"Success",1:"Arg list too long",2:"Permission denied",3:"Address already in use",4:"Address not available",5:"Address family not supported by protocol family",6:"No more processes",7:"Socket already connected",8:"Bad file number",9:"Trying to read unreadable message",10:"Mount device busy",11:"Operation canceled",12:"No children",13:"Connection aborted",14:"Connection refused",15:"Connection reset by peer",16:"File locking deadlock error",17:"Destination address required",18:"Math arg out of domain of func",19:"Quota exceeded",20:"File exists",21:"Bad address",22:"File too large",23:"Host is unreachable",24:"Identifier removed",25:"Illegal byte sequence",26:"Connection already in progress",27:"Interrupted system call",28:"Invalid argument",29:"I/O error",30:"Socket is already connected",31:"Is a directory",32:"Too many symbolic links",33:"Too many open files",34:"Too many links",35:"Message too long",36:"Multihop attempted",37:"File or path name too long",38:"Network interface is not configured",39:"Connection reset by network",40:"Network is unreachable",41:"Too many open files in system",42:"No buffer space available",43:"No such device",44:"No such file or directory",45:"Exec format error",46:"No record locks available",47:"The link has been severed",48:"Not enough core",49:"No message of desired type",50:"Protocol not available",51:"No space left on device",52:"Function not implemented",53:"Socket is not connected",54:"Not a directory",55:"Directory not empty",56:"State not recoverable",57:"Socket operation on non-socket",59:"Not a typewriter",60:"No such device or address",61:"Value too large for defined data type",62:"Previous owner died",63:"Not super-user",64:"Broken pipe",65:"Protocol error",66:"Unknown protocol",67:"Protocol wrong type for socket",68:"Math result not representable",69:"Read only file system",70:"Illegal seek",71:"No such process",72:"Stale file handle",73:"Connection timed out",74:"Text file busy",75:"Cross-device link",100:"Device not a stream",101:"Bad font file fmt",102:"Invalid slot",103:"Invalid request code",104:"No anode",105:"Block device required",106:"Channel number out of range",107:"Level 3 halted",108:"Level 3 reset",109:"Link number out of range",110:"Protocol driver not attached",111:"No CSI structure available",112:"Level 2 halted",113:"Invalid exchange",114:"Invalid request descriptor",115:"Exchange full",116:"No data (for no delay io)",117:"Timer expired",118:"Out of streams resources",119:"Machine is not on the network",120:"Package not installed",121:"The object is remote",122:"Advertise error",123:"Srmount error",124:"Communication error on send",125:"Cross mount point (not really error)",126:"Given log. name not unique",127:"f.d. invalid for this operation",128:"Remote address changed",129:"Can   access a needed shared lib",130:"Accessing a corrupted shared lib",131:".lib section in a.out corrupted",132:"Attempting to link in too many libs",133:"Attempting to exec a shared library",135:"Streams pipe error",136:"Too many users",137:"Socket type not supported",138:"Not supported",139:"Protocol family not supported",140:"Can't send after socket shutdown",141:"Too many references",142:"Host is down",148:"No medium (in tape drive)",156:"Level 2 not synchronized"};var ERRNO_CODES={"EPERM":63,"ENOENT":44,"ESRCH":71,"EINTR":27,"EIO":29,"ENXIO":60,"E2BIG":1,"ENOEXEC":45,"EBADF":8,"ECHILD":12,"EAGAIN":6,"EWOULDBLOCK":6,"ENOMEM":48,"EACCES":2,"EFAULT":21,"ENOTBLK":105,"EBUSY":10,"EEXIST":20,"EXDEV":75,"ENODEV":43,"ENOTDIR":54,"EISDIR":31,"EINVAL":28,"ENFILE":41,"EMFILE":33,"ENOTTY":59,"ETXTBSY":74,"EFBIG":22,"ENOSPC":51,"ESPIPE":70,"EROFS":69,"EMLINK":34,"EPIPE":64,"EDOM":18,"ERANGE":68,"ENOMSG":49,"EIDRM":24,"ECHRNG":106,"EL2NSYNC":156,"EL3HLT":107,"EL3RST":108,"ELNRNG":109,"EUNATCH":110,"ENOCSI":111,"EL2HLT":112,"EDEADLK":16,"ENOLCK":46,"EBADE":113,"EBADR":114,"EXFULL":115,"ENOANO":104,"EBADRQC":103,"EBADSLT":102,"EDEADLOCK":16,"EBFONT":101,"ENOSTR":100,"ENODATA":116,"ETIME":117,"ENOSR":118,"ENONET":119,"ENOPKG":120,"EREMOTE":121,"ENOLINK":47,"EADV":122,"ESRMNT":123,"ECOMM":124,"EPROTO":65,"EMULTIHOP":36,"EDOTDOT":125,"EBADMSG":9,"ENOTUNIQ":126,"EBADFD":127,"EREMCHG":128,"ELIBACC":129,"ELIBBAD":130,"ELIBSCN":131,"ELIBMAX":132,"ELIBEXEC":133,"ENOSYS":52,"ENOTEMPTY":55,"ENAMETOOLONG":37,"ELOOP":32,"EOPNOTSUPP":138,"EPFNOSUPPORT":139,"ECONNRESET":15,"ENOBUFS":42,"EAFNOSUPPORT":5,"EPROTOTYPE":67,"ENOTSOCK":57,"ENOPROTOOPT":50,"ESHUTDOWN":140,"ECONNREFUSED":14,"EADDRINUSE":3,"ECONNABORTED":13,"ENETUNREACH":40,"ENETDOWN":38,"ETIMEDOUT":73,"EHOSTDOWN":142,"EHOSTUNREACH":23,"EINPROGRESS":26,"EALREADY":7,"EDESTADDRREQ":17,"EMSGSIZE":35,"EPROTONOSUPPORT":66,"ESOCKTNOSUPPORT":137,"EADDRNOTAVAIL":4,"ENETRESET":39,"EISCONN":30,"ENOTCONN":53,"ETOOMANYREFS":141,"EUSERS":136,"EDQUOT":19,"ESTALE":72,"ENOTSUP":138,"ENOMEDIUM":148,"EILSEQ":25,"EOVERFLOW":61,"ECANCELED":11,"ENOTRECOVERABLE":56,"EOWNERDEAD":62,"ESTRPIPE":135};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:"/",initialized:false,ignorePermissions:true,ErrnoError:class extends Error{constructor(errno){super(ERRNO_MESSAGES[errno]);this.name="ErrnoError";this.errno=errno;for(var key in ERRNO_CODES){if(ERRNO_CODES[key]===errno){this.code=key;break}}}},genericErrors:{},filesystems:null,syncFSRequests:0,FSStream:class{constructor(){this.shared={}}get object(){return this.node}set object(val){this.node=val}get isRead(){return(this.flags&2097155)!==1}get isWrite(){return(this.flags&2097155)!==0}get isAppend(){return this.flags&1024}get flags(){return this.shared.flags}set flags(val){this.shared.flags=val}get position(){return this.shared.position}set position(val){this.shared.position=val}},FSNode:class{constructor(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev;this.readMode=292|73;this.writeMode=146}get read(){return(this.mode&this.readMode)===this.readMode}set read(val){val?this.mode|=this.readMode:this.mode&=~this.readMode}get write(){return(this.mode&this.writeMode)===this.writeMode}set write(val){val?this.mode|=this.writeMode:this.mode&=~this.writeMode}get isFolder(){return FS.isDir(this.mode)}get isDevice(){return FS.isChrdev(this.mode)}},lookupPath(path,opts={}){path=PATH_FS.resolve(path);if(!path)return{path:"",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split("/").filter(p=>!!p);var current=FS.root;var current_path="/";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!=="/"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){assert(typeof parent=="object");var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=["r","w","rw"][flag&3];if(flag&512){perms+="w"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes("r")&&!(node.mode&292)){return 2}else if(perms.includes("w")&&!(node.mode&146)){return 2}else if(perms.includes("x")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){if(!FS.isDir(dir.mode))return 54;var errCode=FS.nodePermissions(dir,"x");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,"wx")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,"wx");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!=="r"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},dupStream(origStream,fd=-1){var stream=FS.createStream(origStream,fd);stream.stream_ops?.dup?.(stream);return stream},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;stream.stream_ops.open?.(stream)},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push(...m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate=="function"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){assert(FS.syncFSRequests>0);FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){if(typeof type=="string"){throw type}var root=mountpoint==="/";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);assert(idx!==-1);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name==="."||name===".."){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split("/");var d="";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+="/"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev=="undefined"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!=="."){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,"w");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat(path){return FS.stat(path,true)},chmod(path,mode,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown(path,uid,gid,dontFollow){var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path=="string"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,"w");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open(path,flags,mode){if(path===""){throw new FS.ErrnoError(44)}flags=typeof flags=="string"?FS_modeStringToFlags(flags):flags;mode=typeof mode=="undefined"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path=="object"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module["logReadFiles"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){assert(offset>=0);if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){assert(offset>=0);if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!="undefined";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){assert(offset>=0);if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||"binary";if(opts.encoding!=="utf8"&&opts.encoding!=="binary"){throw new Error(`Invalid encoding type "${opts.encoding}"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding==="utf8"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding==="binary"){ret=buf}FS.close(stream);return ret},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data=="string"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error("Unsupported data type")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,"x");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir("/tmp");FS.mkdir("/home");FS.mkdir("/home/<USER>")},createDefaultDevices(){FS.mkdir("/dev");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev("/dev/null",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev("/dev/tty",FS.makedev(5,0));FS.mkdev("/dev/tty1",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice("/dev","random",randomByte);FS.createDevice("/dev","urandom",randomByte);FS.mkdir("/dev/shm");FS.mkdir("/dev/shm/tmp")},createSpecialDirectories(){FS.mkdir("/proc");var proc_self=FS.mkdir("/proc/self");FS.mkdir("/proc/self/fd");FS.mount({mount(){var node=FS.createNode(proc_self,"fd",16384|511,73);node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:"fake"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},"/proc/self/fd")},createStandardStreams(){if(Module["stdin"]){FS.createDevice("/dev","stdin",Module["stdin"])}else{FS.symlink("/dev/tty","/dev/stdin")}if(Module["stdout"]){FS.createDevice("/dev","stdout",null,Module["stdout"])}else{FS.symlink("/dev/tty","/dev/stdout")}if(Module["stderr"]){FS.createDevice("/dev","stderr",null,Module["stderr"])}else{FS.symlink("/dev/tty1","/dev/stderr")}var stdin=FS.open("/dev/stdin",0);var stdout=FS.open("/dev/stdout",1);var stderr=FS.open("/dev/stderr",1);assert(stdin.fd===0,`invalid handle for stdin (${stdin.fd})`);assert(stdout.fd===1,`invalid handle for stdout (${stdout.fd})`);assert(stderr.fd===2,`invalid handle for stderr (${stderr.fd})`)},staticInit(){[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack="<generic error, no stack>"});FS.nameTable=new Array(4096);FS.mount(MEMFS,{},"/");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={"MEMFS":MEMFS}},init(input,output,error){assert(!FS.init.initialized,"FS.init was previously called. If you want to initialize later with custom parameters, remove any earlier calls (note that one is automatically added to the generated code)");FS.init.initialized=true;Module["stdin"]=input||Module["stdin"];Module["stdout"]=output||Module["stdout"];Module["stderr"]=error||Module["stderr"];FS.createStandardStreams()},quit(){FS.init.initialized=false;_fflush(0);for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path==="/"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent=="string"?parent:FS.getPath(parent);var parts=path.split("/").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent=="string"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data=="string"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent=="string"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output?.buffer?.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!="undefined"){throw new Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error("Cannot load without read() or XMLHttpRequest.")}},createLazyFile(parent,name,url,canRead,canWrite){class LazyUint8Array{constructor(){this.lengthKnown=false;this.chunks=[]}get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]}setDataGetter(getter){this.getter=getter}cacheLength(){var xhr=new XMLHttpRequest;xhr.open("HEAD",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);var datalength=Number(xhr.getResponseHeader("Content-length"));var header;var hasByteServing=(header=xhr.getResponseHeader("Accept-Ranges"))&&header==="bytes";var usesGzip=(header=xhr.getResponseHeader("Content-Encoding"))&&header==="gzip";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error("invalid range ("+from+", "+to+") or no bytes requested!");if(to>datalength-1)throw new Error("only "+datalength+" bytes available! programmer error!");var xhr=new XMLHttpRequest;xhr.open("GET",url,false);if(datalength!==chunkSize)xhr.setRequestHeader("Range","bytes="+from+"-"+to);xhr.responseType="arraybuffer";if(xhr.overrideMimeType){xhr.overrideMimeType("text/plain; charset=x-user-defined")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error("Couldn't load "+url+". Status: "+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||"",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]=="undefined"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]=="undefined")throw new Error("doXHR failed!");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out("LazyFiles on gzip forces download of the whole file when length is accessed")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true}get length(){if(!this.lengthKnown){this.cacheLength()}return this._length}get chunkSize(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}if(typeof XMLHttpRequest!="undefined"){if(!ENVIRONMENT_IS_WORKER)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var lazyArray=new LazyUint8Array;var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=(...args)=>{FS.forceLoadFile(node);return fn(...args)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);assert(size>=0);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node},absolutePath(){abort("FS.absolutePath has been removed; use PATH_FS.resolve instead")},createFolder(){abort("FS.createFolder has been removed; use FS.mkdir instead")},createLink(){abort("FS.createLink has been removed; use FS.symlink instead")},joinPath(){abort("FS.joinPath has been removed; use PATH.join instead")},mmapAlloc(){abort("FS.mmapAlloc has been replaced by the top level function mmapAlloc")},standardizePath(){abort("FS.standardizePath has been removed; use PATH.normalize instead")}};var UTF8ToString=(ptr,maxBytesToRead)=>{assert(typeof ptr=="number",`UTF8ToString expects a number (got ${typeof ptr})`);return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):""};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat(func,path,buf){var stat=func(path);HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get(){assert(SYSCALLS.varargs!=undefined);var ret=HEAP32[+SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp(){return SYSCALLS.get()},getStr(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream}};function ___syscall_fcntl64(fd,cmd,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(cmd){case 0:{var arg=SYSCALLS.get();if(arg<0){return-28}while(FS.streams[arg]){arg++}var newStream;newStream=FS.dupStream(stream,arg);return newStream.fd}case 1:case 2:return 0;case 3:return stream.flags;case 4:{var arg=SYSCALLS.get();stream.flags|=arg;return 0}case 12:{var arg=SYSCALLS.getp();var offset=0;HEAP16[arg+offset>>1]=2;return 0}case 13:case 14:return 0}return-28}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_ioctl(fd,op,varargs){SYSCALLS.varargs=varargs;try{var stream=SYSCALLS.getStreamFromFD(fd);switch(op){case 21509:{if(!stream.tty)return-59;return 0}case 21505:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcgets){var termios=stream.tty.ops.ioctl_tcgets(stream);var argp=SYSCALLS.getp();HEAP32[argp>>2]=termios.c_iflag||0;HEAP32[argp+4>>2]=termios.c_oflag||0;HEAP32[argp+8>>2]=termios.c_cflag||0;HEAP32[argp+12>>2]=termios.c_lflag||0;for(var i=0;i<32;i++){HEAP8[argp+i+17]=termios.c_cc[i]||0}return 0}return 0}case 21510:case 21511:case 21512:{if(!stream.tty)return-59;return 0}case 21506:case 21507:case 21508:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tcsets){var argp=SYSCALLS.getp();var c_iflag=HEAP32[argp>>2];var c_oflag=HEAP32[argp+4>>2];var c_cflag=HEAP32[argp+8>>2];var c_lflag=HEAP32[argp+12>>2];var c_cc=[];for(var i=0;i<32;i++){c_cc.push(HEAP8[argp+i+17])}return stream.tty.ops.ioctl_tcsets(stream.tty,op,{c_iflag:c_iflag,c_oflag:c_oflag,c_cflag:c_cflag,c_lflag:c_lflag,c_cc:c_cc})}return 0}case 21519:{if(!stream.tty)return-59;var argp=SYSCALLS.getp();HEAP32[argp>>2]=0;return 0}case 21520:{if(!stream.tty)return-59;return-28}case 21531:{var argp=SYSCALLS.getp();return FS.ioctl(stream,op,argp)}case 21523:{if(!stream.tty)return-59;if(stream.tty.ops.ioctl_tiocgwinsz){var winsize=stream.tty.ops.ioctl_tiocgwinsz(stream.tty);var argp=SYSCALLS.getp();HEAP16[argp>>1]=winsize[0];HEAP16[argp+2>>1]=winsize[1]}return 0}case 21524:{if(!stream.tty)return-59;return 0}case 21515:{if(!stream.tty)return-59;return 0}default:return-28}}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}function ___syscall_openat(dirfd,path,flags,varargs){SYSCALLS.varargs=varargs;try{path=SYSCALLS.getStr(path);path=SYSCALLS.calculateAt(dirfd,path);var mode=varargs?SYSCALLS.get():0;return FS.open(path,flags,mode).fd}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return-e.errno}}var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{};var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret="";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var BindingError;var throwBindingError=message=>{throw new BindingError(message)};var InternalError;var throwInternalError=message=>{throw new InternalError(message)};var whenDependentTypesAreResolved=(myTypes,dependentTypes,getTypeConverters)=>{myTypes.forEach(function(type){typeDependencies[type]=dependentTypes});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError("Mismatched type converter count")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type "${name}" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){if(!("argPackAdvance"in registeredInstance)){throw new TypeError("registerType registeredInstance requires argPackAdvance")}return sharedRegisterType(rawType,registeredInstance,options)}var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":function(wt){return!!wt},"toWireType":function(destructors,o){return o?trueValue:falseValue},"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":function(pointer){return this["fromWireType"](HEAPU8[pointer])},destructorFunction:null})};var emval_freelist=[];var emval_handles=[];var __emval_decref=handle=>{if(handle>9&&0===--emval_handles[handle+1]){assert(emval_handles[handle]!==undefined,`Decref for unallocated handle.`);emval_handles[handle]=undefined;emval_freelist.push(handle)}};var count_emval_handles=()=>emval_handles.length/2-5-emval_freelist.length;var init_emval=()=>{emval_handles.push(0,1,undefined,1,null,1,true,1,false,1);assert(emval_handles.length===5*2);Module["count_emval_handles"]=count_emval_handles};var Emval={toValue:handle=>{if(!handle){throwBindingError("Cannot use deleted val. handle = "+handle)}assert(handle===2||emval_handles[handle]!==undefined&&handle%2===0,`invalid handle: ${handle}`);return emval_handles[handle]},toHandle:value=>{switch(value){case undefined:return 2;case null:return 4;case true:return 6;case false:return 8;default:{const handle=emval_freelist.pop()||emval_handles.length;emval_handles[handle]=value;emval_handles[handle+1]=1;return handle}}}};function readPointer(pointer){return this["fromWireType"](HEAPU32[pointer>>2])}var EmValType={name:"emscripten::val","fromWireType":handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},"toWireType":(destructors,value)=>Emval.toHandle(value),"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":readPointer,destructorFunction:null};var __embind_register_emval=rawType=>registerType(rawType,EmValType);var embindRepr=v=>{if(v===null){return"null"}var t=typeof v;if(t==="object"||t==="array"||t==="function"){return v.toString()}else{return""+v}};var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this["fromWireType"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this["fromWireType"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":value=>value,"toWireType":(destructors,value)=>{if(typeof value!="number"&&typeof value!="boolean"){throw new TypeError(`Cannot convert ${embindRepr(value)} to ${this.name}`)}return value},"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":floatReadValueFromPointer(name,size),destructorFunction:null})};var createNamedFunction=(name,body)=>Object.defineProperty(body,"name",{value:name});var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};function usesDestructorStack(argTypes){for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){return true}}return false}function newFunc(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`)}var dummy=createNamedFunction(constructor.name||"unknownFunctionName",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}function createJsInvoker(argTypes,isClassMethodFunc,returns,isAsync){var needsDestructorStack=usesDestructorStack(argTypes);var argCount=argTypes.length;var argsList="";var argsListWired="";for(var i=0;i<argCount-2;++i){argsList+=(i!==0?", ":"")+"arg"+i;argsListWired+=(i!==0?", ":"")+"arg"+i+"Wired"}var invokerFnBody=`\n        return function (${argsList}) {\n        if (arguments.length !== ${argCount-2}) {\n          throwBindingError('function ' + humanName + ' called with ' + arguments.length + ' arguments, expected ${argCount-2}');\n        }`;if(needsDestructorStack){invokerFnBody+="var destructors = [];\n"}var dtorStack=needsDestructorStack?"destructors":"null";var args1=["humanName","throwBindingError","invoker","fn","runDestructors","retType","classParam"];if(isClassMethodFunc){invokerFnBody+="var thisWired = classParam['toWireType']("+dtorStack+", this);\n"}for(var i=0;i<argCount-2;++i){invokerFnBody+="var arg"+i+"Wired = argType"+i+"['toWireType']("+dtorStack+", arg"+i+");\n";args1.push("argType"+i)}if(isClassMethodFunc){argsListWired="thisWired"+(argsListWired.length>0?", ":"")+argsListWired}invokerFnBody+=(returns||isAsync?"var rv = ":"")+"invoker(fn"+(argsListWired.length>0?", ":"")+argsListWired+");\n";if(needsDestructorStack){invokerFnBody+="runDestructors(destructors);\n"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?"thisWired":"arg"+(i-2)+"Wired";if(argTypes[i].destructorFunction!==null){invokerFnBody+=`${paramName}_dtor(${paramName});\n`;args1.push(`${paramName}_dtor`)}}}if(returns){invokerFnBody+="var ret = retType['fromWireType'](rv);\n"+"return ret;\n"}else{}invokerFnBody+="}\n";invokerFnBody=`if (arguments.length !== ${args1.length}){ throw new Error(humanName + "Expected ${args1.length} closure arguments " + arguments.length + " given."); }\n${invokerFnBody}`;return[args1,invokerFnBody]}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError("argTypes array size mismatch! Must at least get return value and 'this' types!")}assert(!isAsync,"Async bindings are only supported with JSPI.");var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=usesDestructorStack(argTypes);var returns=argTypes[0].name!=="void";var closureArgs=[humanName,throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];for(var i=0;i<argCount-2;++i){closureArgs.push(argTypes[i+2])}if(!needsDestructorStack){for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){if(argTypes[i].destructorFunction!==null){closureArgs.push(argTypes[i].destructorFunction)}}}let[args,invokerFnBody]=createJsInvoker(argTypes,isClassMethodFunc,returns,isAsync);args.push(invokerFnBody);var invokerFn=newFunc(Function,args)(...closureArgs);return createNamedFunction(humanName,invokerFn)}var ensureOverloadTable=(proto,methodName,humanName)=>{if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(...args){if(!proto[methodName].overloadTable.hasOwnProperty(args.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${args.length}) - expects one of (${proto[methodName].overloadTable})!`)}return proto[methodName].overloadTable[args.length].apply(this,args)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}};var exposePublicSymbol=(name,value,numArguments)=>{if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`)}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`)}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments}}};var heap32VectorToArray=(count,firstElement)=>{var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2])}return array};var replacePublicSymbol=(name,value,numArguments)=>{if(!Module.hasOwnProperty(name)){throwInternalError("Replacing nonexistent public symbol")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var dynCallLegacy=(sig,ptr,args)=>{assert("dynCall_"+sig in Module,`bad function pointer type - dynCall function not found for sig '${sig}'`);if(args?.length){assert(args.length===sig.substring(1).replace(/j/g,"--").length)}else{assert(sig.length==1)}var f=Module["dynCall_"+sig];return f(ptr,...args)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}assert(wasmTable.get(funcPtr)==func,"JavaScript-side Wasm function table mirror is out of date!");return func};var dynCall=(sig,ptr,args=[])=>{if(sig.includes("j")){return dynCallLegacy(sig,ptr,args)}assert(getWasmTableEntry(ptr),`missing table entry in dynCall: ${ptr}`);var rtn=getWasmTableEntry(ptr)(...args);return rtn};var getDynCaller=(sig,ptr)=>{assert(sig.includes("j")||sig.includes("p"),"getDynCaller should only be called with i64 sigs");return(...args)=>dynCall(sig,ptr,args)};var embind__requireFunction=(signature,rawFunction)=>{signature=readLatin1String(signature);function makeDynCaller(){if(signature.includes("j")){return getDynCaller(signature,rawFunction)}return getWasmTableEntry(rawFunction)}var fp=makeDynCaller();if(typeof fp!="function"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`)}return fp};var extendError=(baseErrorType,errorName)=>{var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+"\n"+stack.replace(/^Error(:[^\n]*)?\n/,"")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return`${this.name}: ${this.message}`}};return errorClass};var UnboundTypeError;var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var throwUnboundTypeError=(message,types)=>{var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([", "]))};var getFunctionName=signature=>{signature=signature.trim();const argsIndex=signature.indexOf("(");if(argsIndex!==-1){assert(signature[signature.length-1]==")","Parentheses for argument names should match.");return signature.substr(0,argsIndex)}else{return signature}};var __embind_register_function=(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync)=>{var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);name=getFunctionName(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,argTypes=>{var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn,isAsync),argCount-1);return[]})};var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer]:pointer=>HEAPU8[pointer];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift}var isUnsignedType=name.includes("unsigned");var checkAssertions=(value,toTypeName)=>{if(typeof value!="number"&&typeof value!="boolean"){throw new TypeError(`Cannot convert "${embindRepr(value)}" to ${toTypeName}`)}if(value<minRange||value>maxRange){throw new TypeError(`Passing a number "${embindRepr(value)}" from JS side to C/C++ side to an argument of type "${name}", which is outside the valid range [${minRange}, ${maxRange}]!`)}};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0}}else{toWireType=function(destructors,value){checkAssertions(value,this.name);return value}}registerType(primitiveType,{name:name,"fromWireType":fromWireType,"toWireType":toWireType,"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,"fromWireType":decodeMemoryView,"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":decodeMemoryView},{ignoreDuplicateRegistrations:true})};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>{assert(typeof maxBytesToWrite=="number","stringToUTF8(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");return stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite)};var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=name==="std::string";registerType(rawType,{name:name,"fromWireType"(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join("")}_free(value);return str},"toWireType"(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value=="string";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError("Cannot pass non-string to std::string")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError("String has UTF-16 code units that do not fit in 8 bits")}HEAPU8[ptr+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i]}}}if(destructors!==null){destructors.push(_free,base)}return base},"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!="undefined"?new TextDecoder("utf-16le"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{assert(ptr%2==0,"Pointer passed to UTF16ToString must be aligned to two bytes!");var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str="";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{assert(outPtr%2==0,"Pointer passed to stringToUTF16 must be aligned to two bytes!");assert(typeof maxBytesToWrite=="number","stringToUTF16(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");maxBytesToWrite??=2147483647;if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{assert(ptr%4==0,"Pointer passed to UTF32ToString must be aligned to four bytes!");var i=0;var str="";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{assert(outPtr%4==0,"Pointer passed to stringToUTF32 must be aligned to four bytes!");assert(typeof maxBytesToWrite=="number","stringToUTF32(str, outPtr, maxBytesToWrite) is missing the third parameter that specifies the length of the output buffer!");maxBytesToWrite??=2147483647;if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,readCharAt,lengthBytesUTF;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;readCharAt=pointer=>HEAPU16[pointer>>1]}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;readCharAt=pointer=>HEAPU32[pointer>>2]}registerType(rawType,{name:name,"fromWireType":value=>{var length=HEAPU32[value>>2];var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||readCharAt(currentBytePtr)==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},"toWireType":(destructors,value)=>{if(!(typeof value=="string")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length/charSize;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},"argPackAdvance":GenericWireTypeSize,"readValueFromPointer":readPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,"argPackAdvance":0,"fromWireType":()=>undefined,"toWireType":(destructors,o)=>undefined})};var __emscripten_fs_load_embedded_files=ptr=>{do{var name_addr=HEAPU32[ptr>>2];ptr+=4;var len=HEAPU32[ptr>>2];ptr+=4;var content=HEAPU32[ptr>>2];ptr+=4;var name=UTF8ToString(name_addr);FS.createPath("/",PATH.dirname(name),true,true);FS.createDataFile(name,null,HEAP8.subarray(content,content+len),true,true,true)}while(HEAPU32[ptr>>2])};var _abort=()=>{abort("native code called abort()")};var _emscripten_memcpy_js=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){err(`growMemory: Attempted to grow heap from ${b.byteLength} bytes to ${size} bytes, but got error: ${e}`)}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;assert(requestedSize>oldSize);var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){err(`Cannot enlarge memory, requested ${requestedSize} bytes, but the limit is ${maxHeapSize} bytes!`);return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}err(`Failed to grow the heap from ${oldSize} bytes to ${newSize} bytes, not enough memory!`);return false};var ENV={};var getExecutableName=()=>thisProgram||"./this.program";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator=="object"&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8";var env={"USER":"web_user","LOGNAME":"web_user","PATH":"/","PWD":"/","HOME":"/home/<USER>","LANG":lang,"_":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){assert(str.charCodeAt(i)===(str.charCodeAt(i)&255));HEAP8[buffer++]=str.charCodeAt(i)}HEAP8[buffer]=0};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!=="undefined"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var convertI32PairToI53Checked=(lo,hi)=>{assert(lo==lo>>>0||lo==(lo|0));assert(hi===(hi|0));return hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN};function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!=="undefined"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS=="undefined"||!(e.name==="ErrnoError"))throw e;return e.errno}}var isLeapYear=year=>year%4===0&&(year%100!==0||year%400===0);var arraySum=(array,index)=>{var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=(date,days)=>{var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var writeArrayToMemory=(array,buffer)=>{assert(array.length>=0,"writeArrayToMemory array must have a length (should be an array or typed array)");HEAP8.set(array,buffer)};var _strftime=(s,maxsize,format,tm)=>{var tm_zone=HEAPU32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):""};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d","%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_1[rule])}var WEEKDAYS=["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];var MONTHS=["January","February","March","April","May","June","July","August","September","October","November","December"];function leadingSomething(value,digits,character){var str=typeof value=="number"?value.toString():value||"";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,"0")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={"%a":date=>WEEKDAYS[date.tm_wday].substring(0,3),"%A":date=>WEEKDAYS[date.tm_wday],"%b":date=>MONTHS[date.tm_mon].substring(0,3),"%B":date=>MONTHS[date.tm_mon],"%C":date=>{var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},"%d":date=>leadingNulls(date.tm_mday,2),"%e":date=>leadingSomething(date.tm_mday,2," "),"%g":date=>getWeekBasedYear(date).toString().substring(2),"%G":getWeekBasedYear,"%H":date=>leadingNulls(date.tm_hour,2),"%I":date=>{var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},"%j":date=>leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3),"%m":date=>leadingNulls(date.tm_mon+1,2),"%M":date=>leadingNulls(date.tm_min,2),"%n":()=>"\n","%p":date=>{if(date.tm_hour>=0&&date.tm_hour<12){return"AM"}return"PM"},"%S":date=>leadingNulls(date.tm_sec,2),"%t":()=>"\t","%u":date=>date.tm_wday||7,"%U":date=>{var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},"%V":date=>{var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},"%w":date=>date.tm_wday,"%W":date=>{var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},"%y":date=>(date.tm_year+1900).toString().substring(2),"%Y":date=>date.tm_year+1900,"%z":date=>{var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?"+":"-")+String("0000"+off).slice(-4)},"%Z":date=>date.tm_zone,"%%":()=>"%"};pattern=pattern.replace(/%%/g,"\0\0");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,"g"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\0\0/g,"%");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1};var _strftime_l=(s,maxsize,format,tm,loc)=>_strftime(s,maxsize,format,tm);var getCFunc=ident=>{var func=Module["_"+ident];assert(func,"Cannot call unknown function "+ident+", make sure it is exported");return func};var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={"string":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},"array":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType==="string"){return UTF8ToString(ret)}if(returnType==="boolean")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;assert(returnType!=="array",'Return type should not be "array".');if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func(...cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};var cwrap=(ident,returnType,argTypes,opts)=>(...args)=>ccall(ident,returnType,argTypes,args,opts);FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();Module["FS_createPath"]=FS.createPath;Module["FS_createDataFile"]=FS.createDataFile;Module["FS_createPreloadedFile"]=FS.createPreloadedFile;Module["FS_unlink"]=FS.unlink;Module["FS_createLazyFile"]=FS.createLazyFile;Module["FS_createDevice"]=FS.createDevice;embind_init_charCodes();BindingError=Module["BindingError"]=class BindingError extends Error{constructor(message){super(message);this.name="BindingError"}};InternalError=Module["InternalError"]=class InternalError extends Error{constructor(message){super(message);this.name="InternalError"}};init_emval();UnboundTypeError=Module["UnboundTypeError"]=extendError(Error,"UnboundTypeError");function checkIncomingModuleAPI(){ignoredModuleProp("fetchSettings")}var wasmImports={__cxa_throw:___cxa_throw,__syscall_fcntl64:___syscall_fcntl64,__syscall_ioctl:___syscall_ioctl,__syscall_openat:___syscall_openat,_embind_register_bigint:__embind_register_bigint,_embind_register_bool:__embind_register_bool,_embind_register_emval:__embind_register_emval,_embind_register_float:__embind_register_float,_embind_register_function:__embind_register_function,_embind_register_integer:__embind_register_integer,_embind_register_memory_view:__embind_register_memory_view,_embind_register_std_string:__embind_register_std_string,_embind_register_std_wstring:__embind_register_std_wstring,_embind_register_void:__embind_register_void,_emscripten_fs_load_embedded_files:__emscripten_fs_load_embedded_files,abort:_abort,call_js_checkenv:call_js_checkenv,call_js_exec:call_js_exec,call_js_result:call_js_result,emscripten_memcpy_js:_emscripten_memcpy_js,emscripten_resize_heap:_emscripten_resize_heap,environ_get:_environ_get,environ_sizes_get:_environ_sizes_get,fd_close:_fd_close,fd_read:_fd_read,fd_seek:_fd_seek,fd_write:_fd_write,strftime_l:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=createExportWrapper("__wasm_call_ctors");var _ParseRaw=Module["_ParseRaw"]=createExportWrapper("ParseRaw");var _SetWorkerMode=Module["_SetWorkerMode"]=createExportWrapper("SetWorkerMode");var _Init=Module["_Init"]=createExportWrapper("Init");var _ExportFlamegraphCpu=Module["_ExportFlamegraphCpu"]=createExportWrapper("ExportFlamegraphCpu");var _ExportFlamegraphMem=Module["_ExportFlamegraphMem"]=createExportWrapper("ExportFlamegraphMem");var _ExportFlamegraphComboDiff=Module["_ExportFlamegraphComboDiff"]=createExportWrapper("ExportFlamegraphComboDiff");var ___getTypeName=createExportWrapper("__getTypeName");var _fflush=createExportWrapper("fflush");var _free=Module["_free"]=createExportWrapper("free");var _malloc=Module["_malloc"]=createExportWrapper("malloc");var _emscripten_stack_init=()=>(_emscripten_stack_init=wasmExports["emscripten_stack_init"])();var _emscripten_stack_get_free=()=>(_emscripten_stack_get_free=wasmExports["emscripten_stack_get_free"])();var _emscripten_stack_get_base=()=>(_emscripten_stack_get_base=wasmExports["emscripten_stack_get_base"])();var _emscripten_stack_get_end=()=>(_emscripten_stack_get_end=wasmExports["emscripten_stack_get_end"])();var stackSave=createExportWrapper("stackSave");var stackRestore=createExportWrapper("stackRestore");var stackAlloc=createExportWrapper("stackAlloc");var _emscripten_stack_get_current=()=>(_emscripten_stack_get_current=wasmExports["emscripten_stack_get_current"])();var ___cxa_is_pointer_type=createExportWrapper("__cxa_is_pointer_type");var dynCall_jiji=Module["dynCall_jiji"]=createExportWrapper("dynCall_jiji");var dynCall_viijii=Module["dynCall_viijii"]=createExportWrapper("dynCall_viijii");var dynCall_iiiiij=Module["dynCall_iiiiij"]=createExportWrapper("dynCall_iiiiij");var dynCall_iiiiijj=Module["dynCall_iiiiijj"]=createExportWrapper("dynCall_iiiiijj");var dynCall_iiiiiijj=Module["dynCall_iiiiiijj"]=createExportWrapper("dynCall_iiiiiijj");var ___emscripten_embedded_file_data=Module["___emscripten_embedded_file_data"]=43412;var ___start_em_js=Module["___start_em_js"]=59716;var ___stop_em_js=Module["___stop_em_js"]=60956;Module["addRunDependency"]=addRunDependency;Module["removeRunDependency"]=removeRunDependency;Module["FS_createPath"]=FS.createPath;Module["FS_createLazyFile"]=FS.createLazyFile;Module["FS_createDevice"]=FS.createDevice;Module["ccall"]=ccall;Module["cwrap"]=cwrap;Module["FS_createPreloadedFile"]=FS.createPreloadedFile;Module["FS_createDataFile"]=FS.createDataFile;Module["FS_unlink"]=FS.unlink;var missingLibrarySymbols=["writeI53ToI64","writeI53ToI64Clamped","writeI53ToI64Signaling","writeI53ToU64Clamped","writeI53ToU64Signaling","readI53FromI64","readI53FromU64","convertI32PairToI53","convertU32PairToI53","exitJS","ydayFromDate","inetPton4","inetNtop4","inetPton6","inetNtop6","readSockaddr","writeSockaddr","getCallstack","emscriptenLog","convertPCtoSourceLocation","readEmAsmArgs","jstoi_q","listenOnce","autoResumeAudioContext","handleException","keepRuntimeAlive","runtimeKeepalivePush","runtimeKeepalivePop","callUserCallback","maybeExit","asmjsMangle","HandleAllocator","getNativeTypeSize","STACK_SIZE","STACK_ALIGN","POINTER_SIZE","ASSERTIONS","uleb128Encode","sigToWasmTypes","generateFuncType","convertJsFunctionToWasm","getEmptyTableSlot","updateTableMap","getFunctionAddress","addFunction","removeFunction","reallyNegative","unSign","strLen","reSign","formatString","intArrayToString","AsciiToString","stringToNewUTF8","registerKeyEventCallback","maybeCStringToJsString","findEventTarget","getBoundingClientRect","fillMouseEventData","registerMouseEventCallback","registerWheelEventCallback","registerUiEventCallback","registerFocusEventCallback","fillDeviceOrientationEventData","registerDeviceOrientationEventCallback","fillDeviceMotionEventData","registerDeviceMotionEventCallback","screenOrientation","fillOrientationChangeEventData","registerOrientationChangeEventCallback","fillFullscreenChangeEventData","registerFullscreenChangeEventCallback","JSEvents_requestFullscreen","JSEvents_resizeCanvasForFullscreen","registerRestoreOldStyle","hideEverythingExceptGivenElement","restoreHiddenElements","setLetterbox","softFullscreenResizeWebGLRenderTarget","doRequestFullscreen","fillPointerlockChangeEventData","registerPointerlockChangeEventCallback","registerPointerlockErrorEventCallback","requestPointerLock","fillVisibilityChangeEventData","registerVisibilityChangeEventCallback","registerTouchEventCallback","fillGamepadEventData","registerGamepadEventCallback","registerBeforeUnloadEventCallback","fillBatteryEventData","battery","registerBatteryEventCallback","setCanvasElementSize","getCanvasElementSize","jsStackTrace","stackTrace","checkWasiClock","wasiRightsToMuslOFlags","wasiOFlagsToMuslOFlags","createDyncallWrapper","safeSetTimeout","setImmediateWrapped","clearImmediateWrapped","polyfillSetImmediate","getPromise","makePromise","idsToPromises","makePromiseCallback","findMatchingCatch","Browser_asyncPrepareDataCounter","setMainLoop","getSocketFromFD","getSocketAddress","FS_mkdirTree","_setNetworkCallback","heapObjectForWebGLType","toTypedArrayIndex","webgl_enable_ANGLE_instanced_arrays","webgl_enable_OES_vertex_array_object","webgl_enable_WEBGL_draw_buffers","webgl_enable_WEBGL_multi_draw","emscriptenWebGLGet","computeUnpackAlignedImageSize","colorChannelsInGlTextureFormat","emscriptenWebGLGetTexPixelData","emscriptenWebGLGetUniform","webglGetUniformLocation","webglPrepareUniformLocationsBeforeFirstUse","webglGetLeftBracePos","emscriptenWebGLGetVertexAttrib","__glGetActiveAttribOrUniform","writeGLArray","registerWebGlEventCallback","runAndAbortIfError","ALLOC_NORMAL","ALLOC_STACK","allocate","writeStringToMemory","writeAsciiToMemory","setErrNo","demangle","getFunctionArgsName","requireRegisteredType","createJsInvokerSignature","init_embind","getBasestPointer","registerInheritedInstance","unregisterInheritedInstance","getInheritedInstance","getInheritedInstanceCount","getLiveInheritedInstances","enumReadValueFromPointer","genericPointerToWireType","constNoSmartPtrRawPointerToWireType","nonConstNoSmartPtrRawPointerToWireType","init_RegisteredPointer","RegisteredPointer","RegisteredPointer_fromWireType","runDestructor","releaseClassHandle","detachFinalizer","attachFinalizer","makeClassHandle","init_ClassHandle","ClassHandle","throwInstanceAlreadyDeleted","flushPendingDeletes","setDelayFunction","RegisteredClass","shallowCopyInternalPointer","downcastPointer","upcastPointer","validateThis","char_0","char_9","makeLegalFunctionName","getStringOrSymbol","emval_get_global","emval_returnValue","emval_lookupTypes","emval_addMethodCaller"];missingLibrarySymbols.forEach(missingLibrarySymbol);var unexportedSymbols=["run","addOnPreRun","addOnInit","addOnPreMain","addOnExit","addOnPostRun","FS_createFolder","FS_createLink","FS_readFile","out","err","callMain","abort","wasmMemory","wasmExports","stackAlloc","stackSave","stackRestore","getTempRet0","setTempRet0","writeStackCookie","checkStackCookie","intArrayFromBase64","tryParseAsDataURI","convertI32PairToI53Checked","ptrToString","zeroMemory","getHeapMax","growMemory","ENV","MONTH_DAYS_REGULAR","MONTH_DAYS_LEAP","MONTH_DAYS_REGULAR_CUMULATIVE","MONTH_DAYS_LEAP_CUMULATIVE","isLeapYear","arraySum","addDays","ERRNO_CODES","ERRNO_MESSAGES","DNS","Protocols","Sockets","initRandomFill","randomFill","timers","warnOnce","UNWIND_CACHE","readEmAsmArgsArray","jstoi_s","getExecutableName","dynCallLegacy","getDynCaller","dynCall","asyncLoad","alignMemory","mmapAlloc","wasmTable","noExitRuntime","getCFunc","freeTableIndexes","functionsInTableMap","setValue","getValue","PATH","PATH_FS","UTF8Decoder","UTF8ArrayToString","UTF8ToString","stringToUTF8Array","stringToUTF8","lengthBytesUTF8","intArrayFromString","stringToAscii","UTF16Decoder","UTF16ToString","stringToUTF16","lengthBytesUTF16","UTF32ToString","stringToUTF32","lengthBytesUTF32","stringToUTF8OnStack","writeArrayToMemory","JSEvents","specialHTMLTargets","findCanvasEventTarget","currentFullscreenStrategy","restoreOldWindowedStyle","ExitStatus","getEnvStrings","doReadv","doWritev","promiseMap","uncaughtExceptionCount","exceptionLast","exceptionCaught","ExceptionInfo","Browser","getPreloadedImageData__data","wget","SYSCALLS","preloadPlugins","FS_modeStringToFlags","FS_getMode","FS_stdin_getChar_buffer","FS_stdin_getChar","FS","MEMFS","TTY","PIPEFS","SOCKFS","tempFixedLengthArray","miniTempWebGLFloatBuffers","miniTempWebGLIntBuffers","GL","AL","GLUT","EGL","GLEW","IDBStore","SDL","SDL_gfx","allocateUTF8","allocateUTF8OnStack","InternalError","BindingError","throwInternalError","throwBindingError","registeredTypes","awaitingDependencies","typeDependencies","tupleRegistrations","structRegistrations","sharedRegisterType","whenDependentTypesAreResolved","embind_charCodes","embind_init_charCodes","readLatin1String","getTypeName","getFunctionName","heap32VectorToArray","usesDestructorStack","createJsInvoker","UnboundTypeError","PureVirtualError","GenericWireTypeSize","EmValType","throwUnboundTypeError","ensureOverloadTable","exposePublicSymbol","replacePublicSymbol","extendError","createNamedFunction","embindRepr","registeredInstances","registeredPointers","registerType","integerReadValueFromPointer","floatReadValueFromPointer","readPointer","runDestructors","newFunc","craftInvokerFunction","embind__requireFunction","finalizationRegistry","detachFinalizer_deps","deletionQueue","delayFunction","emval_freelist","emval_handles","emval_symbols","init_emval","count_emval_handles","Emval","emval_methodCallers","reflectConstruct"];unexportedSymbols.forEach(unexportedRuntimeSymbol);var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function stackCheckInit(){_emscripten_stack_init();writeStackCookie()}function run(){if(runDependencies>0){return}stackCheckInit();preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module["calledRun"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module["onRuntimeInitialized"])Module["onRuntimeInitialized"]();assert(!Module["_main"],'compiled without a main, but one is present. if you added it from JS, use Module["onRuntimeInitialized"]');postRun()}if(Module["setStatus"]){Module["setStatus"]("Running...");setTimeout(function(){setTimeout(function(){Module["setStatus"]("")},1);doRun()},1)}else{doRun()}checkStackCookie()}if(Module["preInit"]){if(typeof Module["preInit"]=="function")Module["preInit"]=[Module["preInit"]];while(Module["preInit"].length>0){Module["preInit"].pop()()}}run();


  return moduleArg.ready
}
);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = ToolsModule;
else if (typeof define === 'function' && define['amd'])
  define([], () => ToolsModule);
