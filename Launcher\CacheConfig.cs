namespace RobloxLauncher;

public static class CachePriority
{
    public const string Critical = "Critical";
    public const string High = "High";
    public const string Medium = "Medium";
    public const string Low = "Low";
}

public class CacheConfig
{
    public string Version { get; set; } = "1.0.0";
    public CacheSettingsConfig CacheSettings { get; set; } = new CacheSettingsConfig();
    public Dictionary<string, CacheCategory> Categories { get; set; } = new Dictionary<string, CacheCategory>
    {
        ["gameAssets"] = new CacheCategory
        {
            Enabled = true,
            MaxSizeGB = 20,
            RetentionDays = -1,
            Priority = CachePriority.Critical,
            PermanentCache = true,
            AutoCache = true,
            Extensions = new[] { ".rbxm", ".rbxl", ".mesh", ".png", ".jpg", ".ogg", ".mp3", ".wav", ".mp4", ".webm", ".gif" }
        },
        ["textures"] = new CacheCategory
        {
            Enabled = true,
            MaxSizeGB = 15,
            RetentionDays = -1,
            Priority = CachePriority.Critical,
            PermanentCache = true,
            AutoCache = true,
            Extensions = new[] { ".png", ".jpg", ".jpeg", ".bmp", ".tga", ".dds", ".webp", ".svg", ".ico" }
        },
        ["models"] = new CacheCategory
        {
            Enabled = true,
            MaxSizeGB = 10,
            RetentionDays = -1,
            Priority = CachePriority.Critical,
            PermanentCache = true,
            AutoCache = true,
            Extensions = new[] { ".mesh", ".obj", ".fbx", ".rbxm", ".3ds", ".dae", ".blend" }
        },
        ["scripts"] = new CacheCategory
        {
            Enabled = true,
            MaxSizeGB = 3,
            RetentionDays = -1,
            Priority = CachePriority.Critical,
            PermanentCache = true,
            AutoCache = true,
            Extensions = new[] { ".lua", ".luau", ".rbxs", ".js", ".ts", ".json" }
        },
        ["userData"] = new CacheCategory
        {
            Enabled = true,
            MaxSizeGB = 2,
            RetentionDays = -1,
            Priority = CachePriority.Critical,
            PermanentCache = true,
            AutoCache = true,
            Extensions = new[] { ".json", ".xml", ".cfg", ".ini", ".dat", ".save", ".profile" }
        },
        ["everything"] = new CacheCategory
        {
            Enabled = true,
            MaxSizeGB = 50,
            RetentionDays = -1,
            Priority = CachePriority.Critical,
            PermanentCache = true,
            AutoCache = true,
            Extensions = new[] { "*" }
        }
    };
    public CleanupConfig Cleanup { get; set; } = new CleanupConfig();
    public PerformanceConfig Performance { get; set; } = new PerformanceConfig();
}

public class CacheSettingsConfig
{
    public bool Enabled { get; set; } = true;
    public double MaxTotalSizeGB { get; set; } = 50;
    public int MaxFileSizeMB { get; set; } = 1000;
    public int CompressionLevel { get; set; } = 3;
    public bool EncryptionEnabled { get; set; } = false;
    public bool AggressiveCaching { get; set; } = true;
    public bool CacheEverything { get; set; } = true;
    public bool PermanentStorage { get; set; } = true;
}

public class CacheCategory
{
    public bool Enabled { get; set; } = true;
    public double MaxSizeGB { get; set; } = 1;
    public int RetentionDays { get; set; } = 30;
    public string Priority { get; set; } = "Medium";
    public string[] Extensions { get; set; } = Array.Empty<string>();
    public bool PermanentCache { get; set; } = true;
    public bool AutoCache { get; set; } = true;
}

public class CleanupConfig
{
    public bool AutoCleanupEnabled { get; set; } = false;
    public int CleanupIntervalHours { get; set; } = 168;
    public int LowSpaceThresholdMB { get; set; } = 1000;
    public int AggressiveCleanupThresholdMB { get; set; } = 500;
    public bool PreservePermanentCache { get; set; } = true;
    public bool OnlyCleanTemporary { get; set; } = true;
}

public class PerformanceConfig
{
    public int MaxConcurrentDownloads { get; set; } = 20;
    public int MaxConcurrentWrites { get; set; } = 10;
    public int BufferSizeKB { get; set; } = 256;
    public bool UseMemoryMapping { get; set; } = true;
    public bool PreloadAssets { get; set; } = true;
    public bool BackgroundCaching { get; set; } = true;
    public bool InstantCache { get; set; } = true;
    public bool CacheOnAccess { get; set; } = true;
}

public class CacheMetadata
{
    public string OriginalUrl { get; set; } = "";
    public DateTime CachedDate { get; set; }
    public long OriginalSize { get; set; }
    public long CompressedSize { get; set; }
    public bool IsCompressed { get; set; }
    public string Category { get; set; } = "";
    public string Hash { get; set; } = "";
    public DateTime LastAccessed { get; set; }
    public int AccessCount { get; set; }
}
