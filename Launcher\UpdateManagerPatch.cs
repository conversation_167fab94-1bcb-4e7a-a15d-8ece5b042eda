using System;

namespace RobloxLauncher
{
    public static class UpdateManagerPatch
    {
        public static bool ShouldBlockUpdate(string currentVersion, string latestVersion)
        {
            // Check for format mismatch
            bool currentIsNumeric = currentVersion.Contains(".") && !currentVersion.StartsWith("version-");
            bool latestIsHash = latestVersion.StartsWith("version-");
            
            if (currentIsNumeric && latestIsHash)
            {
                Console.WriteLine($"🚫 PATCH BLOCK: Format mismatch detected!");
                Console.WriteLine($"   Current: '{currentVersion}' (numeric)");
                Console.WriteLine($"   Latest: '{latestVersion}' (hash)");
                Console.WriteLine($"   This is a FALSE POSITIVE - update BLOCKED by patch!");
                return true; // Block the update
            }
            
            return false; // Allow the update
        }
    }
}
