{"Version": "1.0.0", "CacheSettings": {"Enabled": true, "MaxTotalSizeGB": 5.0, "MaxFileSizeMB": 100, "CompressionLevel": 6, "EncryptionEnabled": false}, "Categories": {"gameAssets": {"Enabled": true, "MaxSizeGB": 2.0, "RetentionDays": 30, "Priority": "High", "Extensions": [".rbxm", ".rbxl", ".mesh", ".png", ".jpg", ".ogg", ".mp3"]}, "textures": {"Enabled": true, "MaxSizeGB": 1.5, "RetentionDays": 14, "Priority": "Medium", "Extensions": [".png", ".jpg", ".jpeg", ".bmp", ".tga", ".dds"]}, "models": {"Enabled": true, "MaxSizeGB": 1.0, "RetentionDays": 21, "Priority": "Medium", "Extensions": [".mesh", ".obj", ".fbx", ".rbxm"]}, "scripts": {"Enabled": true, "MaxSizeGB": 0.3, "RetentionDays": 7, "Priority": "Low", "Extensions": [".lua", ".luau", ".rbxs"]}, "userData": {"Enabled": true, "MaxSizeGB": 0.2, "RetentionDays": 365, "Priority": "Critical", "Extensions": [".json", ".xml", ".cfg", ".ini"]}}, "Cleanup": {"AutoCleanupEnabled": true, "CleanupIntervalHours": 24, "LowSpaceThresholdMB": 500, "AggressiveCleanupThresholdMB": 100}, "Performance": {"MaxConcurrentDownloads": 5, "MaxConcurrentWrites": 3, "BufferSizeKB": 64, "UseMemoryMapping": true}}