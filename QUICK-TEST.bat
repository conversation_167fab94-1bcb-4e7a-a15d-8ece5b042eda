@echo off
echo ========================================
echo    ROBLOX PORTABLE LAUNCHER - QUICK TEST
echo ========================================
echo.

echo [1/5] Checking launcher executable...
if exist "Launcher\RobloxLauncher.exe" (
    echo ✅ RobloxLauncher.exe found
) else (
    echo ❌ RobloxLauncher.exe missing
    pause
    exit
)

echo.
echo [2/5] Checking Roblox installation...
if exist "Roblox\RobloxPlayerBeta.exe" (
    echo ✅ Roblox installation found
) else (
    echo ❌ Roblox installation missing
    pause
    exit
)

echo.
echo [3/5] Checking version file...
if exist "Roblox\version.txt" (
    echo ✅ version.txt found
    type "Roblox\version.txt"
) else (
    echo ❌ version.txt missing
)

echo.
echo [4/5] Checking settings file...
if exist "Data\settings.json" (
    echo ✅ settings.json found
) else (
    echo ❌ settings.json missing
)

echo.
echo [5/5] Checking cache directory...
if exist "Cache" (
    echo ✅ Cache directory found
) else (
    echo ❌ Cache directory missing
)

echo.
echo ========================================
echo    SYSTEM STATUS: READY ✅
echo ========================================
echo.
echo Starting launcher in 3 seconds...
timeout /t 3 /nobreak >nul

cd Launcher
start RobloxLauncher.exe
cd ..

echo.
echo ✅ Launcher started successfully!
echo.
echo If you see any error messages, check:
echo - FINAL-SYSTEM-STATUS.md
echo - USER-GUIDE-FINAL.md
echo.
pause
