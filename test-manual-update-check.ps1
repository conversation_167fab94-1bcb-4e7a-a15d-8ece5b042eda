# Test manual update check
Write-Host "=== TESTING MANUAL UPDATE CHECK ==="
Write-Host ""

# Test current vs latest version comparison
Write-Host "1. Current version status:"
if (Test-Path "Data\settings.json") {
    $settings = Get-Content "Data\settings.json" -Raw | ConvertFrom-Json
    Write-Host "   Saved version: $($settings.roblox.version)"
} else {
    Write-Host "   ERROR: Settings file not found"
}

if (Test-Path "Roblox\version.txt") {
    $fileVersion = Get-Content "Roblox\version.txt" -Raw
    Write-Host "   File version: $($fileVersion.Trim())"
} else {
    Write-Host "   ERROR: Version file not found"
}

Write-Host ""
Write-Host "2. Latest version from API:"
try {
    $api = Invoke-RestMethod 'https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer'
    Write-Host "   API version field: $($api.version)"
    Write-Host "   API clientVersionUpload: $($api.clientVersionUpload)"
    
    # Test what our patch should do
    $currentVersion = $settings.roblox.version
    $latestVersion = $api.clientVersionUpload  # This is what causes the problem
    
    Write-Host ""
    Write-Host "3. Format mismatch test:"
    $currentIsNumeric = $currentVersion.Contains(".") -and !$currentVersion.StartsWith("version-")
    $latestIsHash = $latestVersion.StartsWith("version-")
    
    Write-Host "   Current is numeric: $currentIsNumeric"
    Write-Host "   Latest is hash: $latestIsHash"
    
    if ($currentIsNumeric -and $latestIsHash) {
        Write-Host "   RESULT: Format mismatch detected - patch should BLOCK this update"
    } else {
        Write-Host "   RESULT: No format mismatch - normal comparison"
    }
    
} catch {
    Write-Host "   ERROR: Failed to get API data - $($_.Exception.Message)"
}

Write-Host ""
Write-Host "4. Expected behavior:"
Write-Host "   - Launcher should show 'PATCH BLOCK: Format mismatch detected!'"
Write-Host "   - Update should be blocked automatically"
Write-Host "   - No update dialog should appear"

Write-Host ""
Write-Host "=== TEST COMPLETE ==="
