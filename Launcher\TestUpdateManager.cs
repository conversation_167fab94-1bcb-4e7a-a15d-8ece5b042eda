using System;
using System.IO;
using System.Threading.Tasks;

namespace RobloxLauncher
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("🔍 Testing UpdateManager...");
            
            try
            {
                var baseDirectory = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location)!;
                var robloxPath = Path.Combine(baseDirectory, "..", "Roblox");
                var cachePath = Path.Combine(baseDirectory, "..", "Cache");
                var dataPath = Path.Combine(baseDirectory, "..", "Data");
                
                Console.WriteLine($"Base Directory: {baseDirectory}");
                Console.WriteLine($"Roblox Path: {robloxPath}");
                Console.WriteLine($"Data Path: {dataPath}");
                
                // Load settings
                var settings = new Settings();
                var settingsPath = Path.Combine(dataPath, "settings.json");
                if (File.Exists(settingsPath))
                {
                    var json = File.ReadAllText(settingsPath);
                    settings = Newtonsoft.Json.JsonConvert.DeserializeObject<Settings>(json) ?? new Settings();
                    Console.WriteLine("✅ Settings loaded");
                }
                else
                {
                    Console.WriteLine("❌ Settings file not found");
                }
                
                // Create UpdateManager
                var updateManager = new UpdateManager(robloxPath, cachePath, settings);
                Console.WriteLine("✅ UpdateManager created");
                
                // Test current version
                var currentVersion = updateManager.GetCurrentVersionPublic();
                Console.WriteLine($"Current Version: '{currentVersion}'");
                
                // Test latest version
                var latestVersion = await updateManager.GetLatestVersionPublicAsync();
                Console.WriteLine($"Latest Version: '{latestVersion}'");
                
                // Test update check
                var hasUpdate = await updateManager.CheckForUpdatesAsync();
                Console.WriteLine($"Has Update: {hasUpdate}");
                
                Console.WriteLine("\n🔍 Press any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                Console.WriteLine("\n🔍 Press any key to exit...");
                Console.ReadKey();
            }
        }
    }
}
