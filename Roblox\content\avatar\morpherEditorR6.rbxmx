<roblox xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.roblox.com/roblox.xsd" version="4">
	<Meta name="ExplicitAutoJoints">false</Meta>
	<External>null</External>
	<External>nil</External>
	<Item class="Model" referent="RBXC893186D5D6E43D698E52B5CB9357A3C">
		<Properties>
			<CoordinateFrame name="ModelInPrimary">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
				<R00>1</R00>
				<R01>0</R01>
				<R02>0</R02>
				<R10>0</R10>
				<R11>1</R11>
				<R12>0</R12>
				<R20>0</R20>
				<R21>0</R21>
				<R22>1</R22>
			</CoordinateFrame>
			<string name="Name">R6</string>
			<Ref name="PrimaryPart">RBX268D3553F8164BE0944302D499E3B918</Ref>
			<BinaryString name="Tags"></BinaryString>
		</Properties>
		<Item class="Part" referent="RBX268D3553F8164BE0944302D499E3B918">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>10.7362061</X>
					<Y>5.16300297</Y>
					<Z>-2.11117506</Z>
					<R00>0.766051888</R00>
					<R01>0</R01>
					<R02>0.642794013</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>0</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4288914085</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Head</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>1</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Decal" referent="RBX74515EE69B674F5B9B47265BBAA0F4B5">
				<Properties>
					<Color3 name="Color3">4294967295</Color3>
					<token name="Face">5</token>
					<string name="Name">face</string>
					<BinaryString name="Tags"></BinaryString>
					<Content name="Texture"><url>rbxasset://textures/face.png</url></Content>
					<float name="Transparency">0</float>
				</Properties>
			</Item>
			<Item class="SpecialMesh" referent="RBX369F71900D274A8ABADD8C87D24A6274">
				<Properties>
					<token name="LODX">2</token>
					<token name="LODY">2</token>
					<Content name="MeshId"><null></null></Content>
					<token name="MeshType">0</token>
					<string name="Name">Mesh</string>
					<Vector3 name="Offset">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
					</Vector3>
					<Vector3 name="Scale">
						<X>1.25</X>
						<Y>1.25</Y>
						<Z>1.25</Z>
					</Vector3>
					<BinaryString name="Tags"></BinaryString>
					<Content name="TextureId"><null></null></Content>
					<Vector3 name="VertexColor">
						<X>1</X>
						<Y>1</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX5E2008A9AD964ECDA2BA0EF7B220B208">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceCenterAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX4E791BDE38A04E63BE885B15F03A5A37">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>-0.600000024</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceFrontAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX8E95B3EDE2454A38A40AED3FDE1CB260">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.600000024</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HairAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX5D5F15F28B014FB9833CAC66F19ABF80">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.600000024</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HatAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX335EB02A1E034C03BDB8817CC0227D43">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>10.7362061</X>
					<Y>3.66300297</Y>
					<Z>-2.11117506</Z>
					<R00>0.766051888</R00>
					<R01>0</R01>
					<R02>0.642794013</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>0</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4288914085</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">0</float>
				<float name="LeftParamB">0</float>
				<token name="LeftSurface">2</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Torso</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">0</float>
				<float name="RightParamB">0</float>
				<token name="RightSurface">2</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Motor6D" referent="RBX9C1C5E6A2880486DB7DFB4A8A04020B6">
				<Properties>
					<CoordinateFrame name="C0">
						<X>1</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>-0</R12>
						<R20>-1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0.5</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>-0</R12>
						<R20>-1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Right Shoulder</string>
					<Ref name="Part0">RBX335EB02A1E034C03BDB8817CC0227D43</Ref>
					<Ref name="Part1">RBXF656CD68CA394FF3B913BD5A491CF6F1</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX1CD30DCA2965494AB30EE69EBA53F8FB">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-1</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.5</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Left Shoulder</string>
					<Ref name="Part0">RBX335EB02A1E034C03BDB8817CC0227D43</Ref>
					<Ref name="Part1">RBX6746C39D16F440CDAF23D54B9D99B38A</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX811370A866F540F8BFCB20B761879F39">
				<Properties>
					<CoordinateFrame name="C0">
						<X>1</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>-0</R12>
						<R20>-1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.5</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>-0</R12>
						<R20>-1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Right Hip</string>
					<Ref name="Part0">RBX335EB02A1E034C03BDB8817CC0227D43</Ref>
					<Ref name="Part1">RBXF436A38E2161481E82066DF8D79AB417</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX582080B8CB694A459C4C9426168989BF">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-1</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0.5</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Left Hip</string>
					<Ref name="Part0">RBX335EB02A1E034C03BDB8817CC0227D43</Ref>
					<Ref name="Part1">RBX1515CFDCBD8E4B02B9C4053FDCB3F8B7</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX7381C923CEAE43278AF16504EFC3885A">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>-1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>0</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>1</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0</X>
						<Y>-0.5</Y>
						<Z>0</Z>
						<R00>-1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>0</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>1</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Neck</string>
					<Ref name="Part0">RBX335EB02A1E034C03BDB8817CC0227D43</Ref>
					<Ref name="Part1">RBX268D3553F8164BE0944302D499E3B918</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXD34195096ABA40E186A7474B3AFC781E">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyBackAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX2FB8222DE6CC41239355AE2F0BBDCBAF">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>-0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyFrontAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXA0C9D7474FA246ADBA031E196A0F3C2C">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftCollarAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX59DE1914BC834DFDB458FB01E556E376">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">NeckAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXA54892F8A950423FB4053F6B7A067984">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>1</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightCollarAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXCB0F391209744EA88BD6DAF8B29372D4">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistBackAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXB23D0F3F2B824B498B99440814F56721">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistCenterAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX4FC6C216FF3A423287DA99026695F2E2">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>-0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistFrontAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX6746C39D16F440CDAF23D54B9D99B38A">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>9.58712769</X>
					<Y>3.66300297</Y>
					<Z>-1.1469841</Z>
					<R00>0.766051888</R00>
					<R01>0</R01>
					<R02>0.642794013</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>0</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4288914085</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Left Arm</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXB68323D36BEA4B75829DAA61D73E45A3">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftShoulderAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBXF656CD68CA394FF3B913BD5A491CF6F1">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>11.8852844</X>
					<Y>3.66300297</Y>
					<Z>-3.07536602</Z>
					<R00>0.766051888</R00>
					<R01>0</R01>
					<R02>0.642794013</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>0</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4294939796</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Right Arm</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX85979F338051483EAA8A359E3FFB3377">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightShoulderAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX1515CFDCBD8E4B02B9C4053FDCB3F8B7">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>10.3531799</X>
					<Y>1.66300297</Y>
					<Z>-1.78977799</Z>
					<R00>0.766051888</R00>
					<R01>0</R01>
					<R02>0.642794013</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>0</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4294939796</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Left Leg</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
		</Item>
		<Item class="Part" referent="RBXF436A38E2161481E82066DF8D79AB417">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>11.1192322</X>
					<Y>1.66300297</Y>
					<Z>-2.43257213</Z>
					<R00>0.766051888</R00>
					<R01>0</R01>
					<R02>0.642794013</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>0</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4288914085</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">Right Leg</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
		</Item>
		<Item class="Humanoid" referent="RBXC5D6676D84C243E6831E904D5CDCA83F">
			<Properties>
				<bool name="AutoJumpEnabled">true</bool>
				<bool name="AutoRotate">true</bool>
				<bool name="AutomaticScalingEnabled">true</bool>
				<token name="DisplayDistanceType">2</token>
				<float name="HealthDisplayDistance">100</float>
				<token name="HealthDisplayType">0</token>
				<float name="Health_XML">0</float>
				<float name="HipHeight">0</float>
				<Vector3 name="InternalBodyScale">
					<X>1</X>
					<Y>1</Y>
					<Z>1</Z>
				</Vector3>
				<float name="InternalHeadScale">1</float>
				<float name="JumpPower">50</float>
				<float name="MaxHealth">0</float>
				<float name="MaxSlopeAngle">89</float>
				<string name="Name">Humanoid</string>
				<float name="NameDisplayDistance">100</float>
				<token name="NameOcclusion">2</token>
				<token name="RigType">0</token>
				<BinaryString name="Tags"></BinaryString>
				<float name="WalkSpeed">16</float>
			</Properties>
		</Item>
		<Item class="Part" referent="RBXC27DD24388A54334A651B4AE62D07FEC">
			<Properties>
				<bool name="Anchored">true</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>10.7362061</X>
					<Y>3.66300297</Y>
					<Z>-2.11117506</Z>
					<R00>0.766051888</R00>
					<R01>2.24691483e-39</R01>
					<R02>0.642794013</R02>
					<R10>2.93314189e-39</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>-0.642794013</R20>
					<R21>-1.88538683e-39</R21>
					<R22>0.766051888</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4279069100</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">0</float>
				<float name="LeftParamB">0</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<token name="Material">256</token>
				<string name="Name">HumanoidRootPart</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">0</float>
				<float name="RightParamB">0</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">1</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Motor6D" referent="RBXA972ADF800B14BAA8DF5D3AB50982599">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
						<R00>-1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>0</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>1</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
						<R00>-1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>0</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>1</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">RootJoint</string>
					<Ref name="Part0">RBXC27DD24388A54334A651B4AE62D07FEC</Ref>
					<Ref name="Part1">RBX335EB02A1E034C03BDB8817CC0227D43</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
		</Item>
	</Item>
</roblox>