@echo off
echo ========================================
echo    ROBLOX PORTABLE SYSTEM REGISTRATION
echo ========================================
echo.
echo This will register Roblox Portable with Windows system
echo so it works like a normal Roblox installation.
echo.
echo WARNING: This requires Administrator privileges!
echo.
pause

cd /d "%~dp0"

:: Check for admin privileges
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo ERROR: This script must be run as Administrator!
    echo Right-click and select "Run as administrator"
    pause
    exit /b 1
)

echo [1/6] Stopping any running Roblox processes...
taskkill /F /IM RobloxPlayerBeta.exe >nul 2>&1
taskkill /F /IM RobloxPlayerInstaller.exe >nul 2>&1
taskkill /F /IM RobloxLauncher.exe >nul 2>&1

echo [2/6] Cleaning existing Roblox registry entries...
reg.exe delete "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation" /f >nul 2>&1
reg.exe delete "HKLM\SOFTWARE\Classes\roblox" /f >nul 2>&1
reg.exe delete "HKLM\SOFTWARE\Classes\roblox-player" /f >nul 2>&1
reg.exe delete "HKLM\SOFTWARE\Classes\roblox-studio" /f >nul 2>&1

echo [3/6] Removing existing Roblox installations...
rmdir /s /q "C:\Program Files (x86)\Roblox" >nul 2>&1
rmdir /s /q "%ALLUSERSPROFILE%\Roblox" >nul 2>&1
rmdir /s /q "%ALLUSERSPROFILE%\Microsoft\Windows\Start Menu\Programs\Roblox" >nul 2>&1
rmdir /s /q "%USERPROFILE%\AppData\Local\Roblox" >nul 2>&1

echo [4/6] Removing desktop shortcuts...
del /q "%USERPROFILE%\Desktop\*Roblox Player.lnk" >nul 2>&1
del /q "%USERPROFILE%\Desktop\*Roblox Studio.lnk" >nul 2>&1

echo [5/6] Creating symbolic link to portable Roblox...
set "PortableRoblox=%~dp0Roblox"
set "SystemRoblox=C:\Program Files (x86)\Roblox"

if not exist "%PortableRoblox%" (
    echo ERROR: Portable Roblox folder not found at: %PortableRoblox%
    pause
    exit /b 1
)

mklink /D "%SystemRoblox%" "%PortableRoblox%"
if %errorLevel% neq 0 (
    echo ERROR: Failed to create symbolic link!
    pause
    exit /b 1
)

echo [6/6] Registering Roblox with Windows...

:: Find RobloxPlayerBeta.exe
set "RobloxExe="
for /r "%SystemRoblox%" %%i in (RobloxPlayerBeta.exe) do (
    set "RobloxExe=%%i"
    set "RobloxDir=%%~dpi"
    goto :found
)

:found
if "%RobloxExe%"=="" (
    echo ERROR: RobloxPlayerBeta.exe not found in portable installation!
    pause
    exit /b 1
)

echo Found Roblox at: %RobloxExe%

:: Register with Windows Registry
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player" /ve /t REG_SZ /d "%RobloxDir%" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player" /v "baseHost" /t REG_SZ /d "www.roblox.com" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player" /v "version" /t REG_SZ /d "%RobloxDir%" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player" /v "clientExe" /t REG_SZ /d "%RobloxExe%" /f

Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player\Capabilities" /v "ApplicationDescription" /t REG_SZ /d "Roblox" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player\Capabilities" /v "ApplicationName" /t REG_SZ /d "Roblox Player" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player\Capabilities" /v "ApplicationIcon" /t REG_EXPAND_SZ /d "\"%RobloxExe%\",0" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player\Capabilities\UrlAssociations" /v "roblox-player" /t REG_SZ /d "roblox-player" /f
Reg.exe add "HKLM\SOFTWARE\WOW6432Node\ROBLOX Corporation\Environments\roblox-player\Capabilities\UrlAssociations" /v "roblox" /t REG_SZ /d "roblox" /f

:: Register URL protocols
Reg.exe add "HKLM\SOFTWARE\Classes\roblox" /ve /t REG_SZ /d "URL: Roblox Protocol" /f
Reg.exe add "HKLM\SOFTWARE\Classes\roblox" /v "URL Protocol" /t REG_SZ /d "" /f
Reg.exe add "HKLM\SOFTWARE\Classes\roblox\DefaultIcon" /ve /t REG_SZ /d "%RobloxExe%" /f
Reg.exe add "HKLM\SOFTWARE\Classes\roblox\shell\open\command" /ve /t REG_SZ /d "\"%RobloxExe%\" %%1" /f

Reg.exe add "HKLM\SOFTWARE\Classes\roblox-player" /ve /t REG_SZ /d "URL: Roblox Protocol" /f
Reg.exe add "HKLM\SOFTWARE\Classes\roblox-player" /v "URL Protocol" /t REG_SZ /d "" /f
Reg.exe add "HKLM\SOFTWARE\Classes\roblox-player\DefaultIcon" /ve /t REG_SZ /d "%RobloxExe%" /f
Reg.exe add "HKLM\SOFTWARE\Classes\roblox-player\shell\open\command" /ve /t REG_SZ /d "\"%RobloxExe%\" %%1" /f

echo.
echo ========================================
echo    REGISTRATION COMPLETED SUCCESSFULLY!
echo ========================================
echo.
echo Roblox Portable is now registered with Windows.
echo You can now:
echo - Click Roblox links in browsers
echo - Use roblox:// URLs
echo - Launch games directly from websites
echo.
echo The portable installation will be used instead
echo of downloading a new copy each time.
echo.
pause
