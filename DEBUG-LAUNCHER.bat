@echo off
echo ========================================
echo DEBUG LAUNCHER - Update Loop Diagnosis
echo ========================================
echo.

echo Current Directory: %CD%
echo.

echo Checking settings file...
if exist "Data\settings.json" (
    echo ✅ settings.json exists
    echo File size: 
    dir "Data\settings.json" | find "settings.json"
    echo.
    echo Current version in settings:
    findstr "version" "Data\settings.json" | head -5
) else (
    echo ❌ settings.json NOT FOUND!
)

echo.
echo Checking Roblox directory...
if exist "Roblox" (
    echo ✅ Roblox directory exists
    dir Roblox | find "File(s)"
    if exist "Roblox\version.txt" (
        echo ✅ version.txt exists
        echo Content:
        type "Roblox\version.txt"
    ) else (
        echo ❌ version.txt NOT FOUND!
    )
    if exist "Roblox\RobloxPlayerBeta.exe" (
        echo ✅ RobloxPlayerBeta.exe exists
    ) else (
        echo ❌ RobloxPlayerBeta.exe NOT FOUND!
    )
) else (
    echo ❌ Roblox directory NOT FOUND!
)

echo.
echo ========================================
echo Starting launcher with debug output...
echo ========================================
echo.

RobloxLauncher.exe

echo.
echo ========================================
echo Post-launch check...
echo ========================================
echo.

echo Checking settings after launch...
if exist "Data\settings.json" (
    echo Current version in settings after launch:
    findstr "version" "Data\settings.json" | head -5
) else (
    echo ❌ settings.json still NOT FOUND!
)

pause
