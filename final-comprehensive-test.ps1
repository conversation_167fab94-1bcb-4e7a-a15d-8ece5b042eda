# Final Comprehensive Test
Write-Host "=== FINAL COMPREHENSIVE TEST ==="
Write-Host ""

# Test 1: API Version Check
Write-Host "1. Testing API version retrieval..."
try {
    $api = Invoke-RestMethod 'https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer'
    Write-Host "   API version field: $($api.version)"
    Write-Host "   API clientVersionUpload: $($api.clientVersionUpload)"
    Write-Host "   Status: SUCCESS"
} catch {
    Write-Host "   Status: FAILED - $($_.Exception.Message)"
}

Write-Host ""

# Test 2: Settings Check
Write-Host "2. Checking settings configuration..."
if (Test-Path "Data\settings.json") {
    $settings = Get-Content "Data\settings.json" -Raw | ConvertFrom-Json
    Write-Host "   Saved version: $($settings.roblox.version)"
    Write-Host "   Auto-update: $($settings.roblox.autoUpdate)"
    Write-Host "   Check on startup: $($settings.roblox.checkOnStartup)"
    Write-Host "   Skip if working: $($settings.roblox.skipUpdateIfWorking)"
    Write-Host "   Status: SUCCESS"
} else {
    Write-Host "   Status: FAILED - Settings file not found"
}

Write-Host ""

# Test 3: Version File Check
Write-Host "3. Checking version file..."
if (Test-Path "Roblox\version.txt") {
    $fileVersion = Get-Content "Roblox\version.txt" -Raw
    $fileVersion = $fileVersion.Trim()
    Write-Host "   Version file content: '$fileVersion'"
    Write-Host "   Status: SUCCESS"
} else {
    Write-Host "   Status: FAILED - Version file not found"
}

Write-Host ""

# Test 4: Version Comparison
Write-Host "4. Testing version comparison logic..."
if ($api.version -eq $settings.roblox.version) {
    Write-Host "   Comparison result: MATCH - No update needed"
    Write-Host "   Status: SUCCESS - No false alerts expected"
} else {
    Write-Host "   Comparison result: MISMATCH - Update would be triggered"
    Write-Host "   API: $($api.version)"
    Write-Host "   Saved: $($settings.roblox.version)"
    Write-Host "   Status: WARNING - Update alert may appear"
}

Write-Host ""

# Test 5: Launcher Startup Test
Write-Host "5. Testing launcher startup (5 second test)..."
try {
    $process = Start-Process -FilePath "Launcher\RobloxLauncher.exe" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 5
    
    if ($process.HasExited) {
        Write-Host "   Launcher behavior: Exited quickly"
        Write-Host "   Exit code: $($process.ExitCode)"
    } else {
        Write-Host "   Launcher behavior: Running normally"
        $process.Kill()
        Write-Host "   Status: SUCCESS - No immediate exit"
    }
} catch {
    Write-Host "   Status: FAILED - $($_.Exception.Message)"
}

Write-Host ""
Write-Host "=== TEST SUMMARY ==="

if ($api.version -eq $settings.roblox.version) {
    Write-Host "RESULT: ALL SYSTEMS OPERATIONAL"
    Write-Host "- Version comparison: CORRECT"
    Write-Host "- False alerts: ELIMINATED"
    Write-Host "- System status: PERFECT"
} else {
    Write-Host "RESULT: VERSION MISMATCH DETECTED"
    Write-Host "- Action needed: Update version files"
    Write-Host "- Expected behavior: Update alert may appear"
}

Write-Host ""
Write-Host "Test completed."
