using Newtonsoft.Json;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;

namespace RobloxLauncher;

public class CacheManager : IDisposable
{
    private const string MetadataExtension = ".meta";
    private readonly string _cacheBasePath;
    private readonly string _configPath;
    private CacheConfig _config;
    private readonly Dictionary<string, CacheCategory> _categories;
    private readonly System.Threading.Timer _cleanupTimer;

    public CacheManager(string cacheBasePath, string configPath)
    {
        _cacheBasePath = cacheBasePath;
        _configPath = configPath;
        _categories = new Dictionary<string, CacheCategory>();
        
        LoadConfiguration();
        InitializeCategories();
        
        // Set up automatic cleanup timer
        var cleanupInterval = TimeSpan.FromHours(_config.Cleanup?.CleanupIntervalHours ?? 24);
        _cleanupTimer = new System.Threading.Timer(PerformAutomaticCleanup, null, cleanupInterval, cleanupInterval);
    }

    private void LoadConfiguration()
    {
        try
        {
            if (File.Exists(_configPath))
            {
                var json = File.ReadAllText(_configPath);
                _config = JsonConvert.DeserializeObject<CacheConfig>(json) ?? new CacheConfig();
            }
            else
            {
                _config = new CacheConfig();
                SaveConfiguration();
            }
        }
        catch
        {
            _config = new CacheConfig();
        }
    }

    private void SaveConfiguration()
    {
        try
        {
            Directory.CreateDirectory(Path.GetDirectoryName(_configPath)!);
            var json = JsonConvert.SerializeObject(_config, Formatting.Indented);
            File.WriteAllText(_configPath, json);
        }
        catch
        {
            // Ignore save errors
        }
    }

    private void InitializeCategories()
    {
        foreach (var category in _config.Categories)
        {
            var categoryPath = Path.Combine(_cacheBasePath, category.Key);
            Directory.CreateDirectory(categoryPath);
            _categories[category.Key] = category.Value;
        }
    }

    public async Task<string?> CacheAssetAsync(string url, string category, byte[] data)
    {
        if (!_config.CacheSettings.Enabled || !_categories.ContainsKey(category))
            return null;

        try
        {
            var hash = ComputeHash(url);
            var extension = GetExtensionFromUrl(url);
            var fileName = $"{hash}{extension}";
            var categoryPath = Path.Combine(_cacheBasePath, category);
            var filePath = Path.Combine(categoryPath, fileName);

            // Check if file already exists
            if (File.Exists(filePath))
                return filePath;

            // For permanent caching, skip size limits check
            var categoryConfig = _categories[category];
            bool isPermanentCache = categoryConfig.RetentionDays == -1;

            // Check category size limits only if not permanent cache
            if (!isPermanentCache && !CheckCategoryLimits(category, data.Length))
            {
                await CleanupCategory(category);
                if (!CheckCategoryLimits(category, data.Length))
                    return null;
            }

            // Compress data if enabled
            var finalData = _config.CacheSettings.CompressionLevel > 0 ? 
                CompressData(data) : data;

            // Write to cache
            await File.WriteAllBytesAsync(filePath, finalData);

            // Create metadata file
            var metadata = new CacheMetadata
            {
                OriginalUrl = url,
                CachedDate = DateTime.UtcNow,
                OriginalSize = data.Length,
                CompressedSize = finalData.Length,
                IsCompressed = _config.CacheSettings.CompressionLevel > 0,
                Category = category
            };

            var metadataPath = filePath + MetadataExtension;
            var metadataJson = JsonConvert.SerializeObject(metadata);
            await File.WriteAllTextAsync(metadataPath, metadataJson);

            return filePath;
        }
        catch
        {
            return null;
        }
    }

    public async Task<byte[]?> GetCachedAssetAsync(string url, string category)
    {
        if (!_config.CacheSettings.Enabled || !_categories.ContainsKey(category))
            return null;

        try
        {
            var hash = ComputeHash(url);
            var extension = GetExtensionFromUrl(url);
            var fileName = $"{hash}{extension}";
            var categoryPath = Path.Combine(_cacheBasePath, category);
            var filePath = Path.Combine(categoryPath, fileName);
            var metadataPath = filePath + MetadataExtension;

            if (!File.Exists(filePath) || !File.Exists(metadataPath))
                return null;

            // Check if cache entry is still valid
            var metadataJson = await File.ReadAllTextAsync(metadataPath);
            var metadata = JsonConvert.DeserializeObject<CacheMetadata>(metadataJson);
            
            if (metadata == null || IsCacheExpired(metadata, category))
            {
                // Clean up expired cache
                File.Delete(filePath);
                File.Delete(metadataPath);
                return null;
            }

            // Read and decompress data
            var data = await File.ReadAllBytesAsync(filePath);
            return metadata.IsCompressed ? DecompressData(data) : data;
        }
        catch
        {
            return null;
        }
    }

    public async Task<bool> PreloadCommonAssetsAsync(IEnumerable<string> urls, string category)
    {
        if (!_config.CacheSettings.Enabled)
            return false;

        var tasks = new List<Task>();
        var semaphore = new SemaphoreSlim(_config.Performance.MaxConcurrentDownloads);

        foreach (var url in urls)
        {
            tasks.Add(PreloadAssetAsync(url, category, semaphore));
        }

        await Task.WhenAll(tasks);
        return true;
    }

    private async Task PreloadAssetAsync(string url, string category, SemaphoreSlim semaphore)
    {
        await semaphore.WaitAsync();
        try
        {
            // Check if already cached
            var cached = await GetCachedAssetAsync(url, category);
            if (cached != null)
                return;

            // Download and cache
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(_config.Performance.BufferSizeKB);
            
            var data = await httpClient.GetByteArrayAsync(url);
            await CacheAssetAsync(url, category, data);
        }
        catch
        {
            // Ignore download errors for preloading
        }
        finally
        {
            semaphore.Release();
        }
    }

    private bool CheckCategoryLimits(string category, long additionalSize)
    {
        if (!_categories.TryGetValue(category, out var categoryConfig))
            return false;

        var categoryPath = Path.Combine(_cacheBasePath, category);
        var currentSize = GetDirectorySize(categoryPath);
        var maxSize = (long)(categoryConfig.MaxSizeGB * 1024 * 1024 * 1024);

        return (currentSize + additionalSize) <= maxSize;
    }

    private Task CleanupCategory(string category)
    {
        if (!_categories.TryGetValue(category, out var categoryConfig))
            return Task.CompletedTask;

        var categoryPath = Path.Combine(_cacheBasePath, category);
        var files = Directory.GetFiles(categoryPath, "*", SearchOption.AllDirectories)
            .Where(f => !f.EndsWith(MetadataExtension))
            .Select(f => new FileInfo(f))
            .OrderBy(f => f.LastAccessTime)
            .ToList();

        var maxSize = (long)(categoryConfig.MaxSizeGB * 1024 * 1024 * 1024);
        var currentSize = files.Sum(f => f.Length);

        // Remove oldest files until we're under the limit
        foreach (var file in files)
        {
            if (currentSize <= maxSize * 0.8) // Leave 20% buffer
                break;

            try
            {
                var metadataPath = file.FullName + MetadataExtension;
                File.Delete(file.FullName);
                if (File.Exists(metadataPath))
                    File.Delete(metadataPath);
                
                currentSize -= file.Length;
            }
            catch
            {
                // Ignore deletion errors
            }
        }

        return Task.CompletedTask;
    }

    private void PerformAutomaticCleanup(object? state)
    {
        if (!_config.Cleanup.AutoCleanupEnabled)
            return;

        try
        {
            foreach (var category in _categories.Keys)
            {
                CleanupCategory(category).Wait();
            }
        }
        catch
        {
            // Ignore cleanup errors
        }
    }

    private bool IsCacheExpired(CacheMetadata metadata, string category)
    {
        if (!_categories.TryGetValue(category, out var categoryConfig))
            return true;

        var expiryDate = metadata.CachedDate.AddDays(categoryConfig.RetentionDays);
        return DateTime.UtcNow > expiryDate;
    }

    private static string ComputeHash(string input)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(Encoding.UTF8.GetBytes(input));
        return Convert.ToHexString(hash)[..16]; // Use first 16 characters
    }

    private static string GetExtensionFromUrl(string url)
    {
        try
        {
            var uri = new Uri(url);
            var path = uri.AbsolutePath;
            var extension = Path.GetExtension(path);
            return string.IsNullOrEmpty(extension) ? ".cache" : extension;
        }
        catch
        {
            return ".cache";
        }
    }

    private static byte[] CompressData(byte[] data)
    {
        using var output = new MemoryStream();
        using var gzip = new GZipStream(output, CompressionLevel.Optimal);
        gzip.Write(data, 0, data.Length);
        gzip.Close();
        return output.ToArray();
    }

    private static byte[] DecompressData(byte[] compressedData)
    {
        using var input = new MemoryStream(compressedData);
        using var gzip = new GZipStream(input, CompressionMode.Decompress);
        using var output = new MemoryStream();
        gzip.CopyTo(output);
        return output.ToArray();
    }

    private static long GetDirectorySize(string directoryPath)
    {
        if (!Directory.Exists(directoryPath))
            return 0;

        try
        {
            return Directory.GetFiles(directoryPath, "*", SearchOption.AllDirectories)
                           .Sum(file => new FileInfo(file).Length);
        }
        catch
        {
            return 0;
        }
    }

    // Auto-cache any asset that gets accessed
    public async Task<string?> AutoCacheAssetAsync(string url, byte[] data)
    {
        try
        {
            // Determine category based on file extension
            var extension = GetExtensionFromUrl(url).ToLower();
            string category = DetermineCategoryFromExtension(extension);

            // Always cache everything - auto-caching is always enabled
            return await CacheAssetAsync(url, category, data);
        }
        catch (Exception ex)
        {
            // Log error but don't fail the operation
            Console.WriteLine($"Auto-cache failed for {url}: {ex.Message}");
            return null;
        }
    }

    private static string DetermineCategoryFromExtension(string extension)
    {
        return extension switch
        {
            ".rbxm" or ".rbxl" or ".mesh" or ".ogg" or ".mp3" or ".wav" or ".mp4" or ".webm" or ".gif" => "gameAssets",
            ".png" or ".jpg" or ".jpeg" or ".bmp" or ".tga" or ".dds" or ".webp" or ".svg" or ".ico" => "textures",
            ".obj" or ".fbx" or ".3ds" or ".dae" or ".blend" => "models",
            ".lua" or ".luau" or ".rbxs" or ".js" or ".ts" => "scripts",
            ".json" or ".xml" or ".cfg" or ".ini" or ".dat" or ".save" or ".profile" => "userData",
            _ => "everything"
        };
    }

    // Enhanced cleanup that preserves permanent cache
    public async Task CleanupNonPermanentCache()
    {
        foreach (var category in _categories)
        {
            if (category.Value.RetentionDays == -1) // Skip permanent cache
                continue;

            await CleanupCategory(category.Key);
        }
    }

    private bool _disposed = false;

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _cleanupTimer?.Dispose();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
