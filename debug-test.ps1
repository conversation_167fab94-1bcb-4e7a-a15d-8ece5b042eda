# Debug test script
Write-Host "Testing Roblox Launcher Debug..."

# Check current version from file
$versionFile = "Roblox\version.txt"
if (Test-Path $versionFile) {
    $currentVersion = Get-Content $versionFile -Raw
    $currentVersion = $currentVersion.Trim()
    Write-Host "Version from file: $currentVersion"
} else {
    Write-Host "Version file not found"
}

# Check version from settings
$settingsFile = "Data\settings.json"
if (Test-Path $settingsFile) {
    $settings = Get-Content $settingsFile -Raw | ConvertFrom-Json
    $settingsVersion = $settings.roblox.version
    Write-Host "Version from settings: $settingsVersion"
} else {
    Write-Host "Settings file not found"
}

# Check latest version from API
try {
    $apiResponse = Invoke-RestMethod 'https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer'
    $latestVersion = $apiResponse.version
    Write-Host "Latest version from API: $latestVersion"

    # Compare versions
    if ($currentVersion -eq $latestVersion) {
        Write-Host "Versions match - no update needed"
    } else {
        Write-Host "Versions differ - update would be triggered"
        Write-Host "   Current: $currentVersion"
        Write-Host "   Latest:  $latestVersion"
    }
} catch {
    Write-Host "Failed to get latest version: $($_.Exception.Message)"
}

# Test launcher startup
Write-Host ""
Write-Host "Testing launcher startup..."
try {
    $process = Start-Process -FilePath "Launcher\RobloxLauncher.exe" -PassThru -WindowStyle Hidden
    Start-Sleep -Seconds 5

    if ($process.HasExited) {
        Write-Host "Launcher exited quickly (Exit Code: $($process.ExitCode))"
    } else {
        Write-Host "Launcher is running"
        $process.Kill()
    }
} catch {
    Write-Host "Failed to start launcher: $($_.Exception.Message)"
}

Write-Host ""
Write-Host "Debug test complete."
