<roblox xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.roblox.com/roblox.xsd" version="4">
	<Meta name="ExplicitAutoJoints">true</Meta>
	<External>null</External>
	<External>nil</External>
	<Item class="Model" referent="RBX0">
		<Properties>
			<BinaryString name="AttributesSerialize"></BinaryString>
			<token name="LevelOfDetail">0</token>
			<CoordinateFrame name="ModelMeshCFrame">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
				<R00>1</R00>
				<R01>0</R01>
				<R02>0</R02>
				<R10>0</R10>
				<R11>1</R11>
				<R12>0</R12>
				<R20>0</R20>
				<R21>0</R21>
				<R22>1</R22>
			</CoordinateFrame>
			<SharedString name="ModelMeshData">yuZpQdnvvUBOTYh1jqZ2cA==</SharedString>
			<Vector3 name="ModelMeshSize">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
			</Vector3>
			<token name="ModelStreamingMode">0</token>
			<string name="Name">MrGrey</string>
			<bool name="NeedsPivotMigration">false</bool>
			<Ref name="PrimaryPart">RBX1</Ref>
			<float name="ScaleFactor">1</float>
			<int64 name="SourceAssetId">-1</int64>
			<BinaryString name="Tags"></BinaryString>
			<OptionalCoordinateFrame name="WorldPivotData">
				<CFrame>
					<X>0</X>
					<Y>2.5</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CFrame>
			</OptionalCoordinateFrame>
		</Properties>
		<Item class="Part" referent="RBX1">
			<Properties>
				<bool name="Anchored">false</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>4.5</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="EnableFluidForces">false</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Head</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>1</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="SpecialMesh" referent="RBX2">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<Content name="MeshId"><null></null></Content>
					<token name="MeshType">0</token>
					<string name="Name">Mesh</string>
					<Vector3 name="Offset">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
					</Vector3>
					<Vector3 name="Scale">
						<X>1.25</X>
						<Y>1.25</Y>
						<Z>1.25</Z>
					</Vector3>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<Content name="TextureId"><null></null></Content>
					<Vector3 name="VertexColor">
						<X>1</X>
						<Y>1</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
			<Item class="Decal" referent="RBX3">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<Color3 name="Color3">
						<R>1</R>
						<G>1</G>
						<B>1</B>
					</Color3>
					<token name="Face">5</token>
					<string name="Name">face</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<Content name="Texture"><url>rbxasset://textures/face.png</url></Content>
					<float name="Transparency">0</float>
					<int name="ZIndex">1</int>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXB8C8BC6B0FD24019883B8EC32FFE6D28">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceCenterAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX7B3CC938F06244F3A45297951844199E">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>-0.600000024</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceFrontAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXC5A3956923324F7382D61970BA81E680">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.600000024</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HairAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX7140EC933DD04E38B9356D206D249EEC">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0.600000024</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HatAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX4">
			<Properties>
				<bool name="Anchored">false</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>0</X>
					<Y>3</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>-0</R02>
					<R10>-0</R10>
					<R11>1</R11>
					<R12>-0</R12>
					<R20>-0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="EnableFluidForces">false</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">0</float>
				<float name="LeftParamB">0</float>
				<token name="LeftSurface">2</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Torso</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">0</float>
				<float name="RightParamB">0</float>
				<token name="RightSurface">2</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Motor6D" referent="RBX5">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="C0">
						<X>1</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>-1</R20>
						<R21>-0</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0.5</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>-1</R20>
						<R21>-0</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="Enabled">true</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Right Shoulder</string>
					<Ref name="Part0">RBX4</Ref>
					<Ref name="Part1">RBX6</Ref>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX7">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="C0">
						<X>-1</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>-0</R00>
						<R01>-0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.5</X>
						<Y>0.5</Y>
						<Z>0</Z>
						<R00>-0</R00>
						<R01>-0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="Enabled">true</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Left Shoulder</string>
					<Ref name="Part0">RBX4</Ref>
					<Ref name="Part1">RBX8</Ref>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX9">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="C0">
						<X>1</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>-1</R20>
						<R21>-0</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.5</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>0</R00>
						<R01>0</R01>
						<R02>1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>-1</R20>
						<R21>-0</R21>
						<R22>-0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="Enabled">true</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Right Hip</string>
					<Ref name="Part0">RBX4</Ref>
					<Ref name="Part1">RBX10</Ref>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX11">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="C0">
						<X>-1</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>-0</R00>
						<R01>-0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0.5</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>-0</R00>
						<R01>-0</R01>
						<R02>-1</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>1</R20>
						<R21>0</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="Enabled">true</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Left Hip</string>
					<Ref name="Part0">RBX4</Ref>
					<Ref name="Part1">RBX12</Ref>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBX13">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="C0">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>-1</R00>
						<R01>-0</R01>
						<R02>-0</R02>
						<R10>0</R10>
						<R11>0</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>1</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0</X>
						<Y>-0.5</Y>
						<Z>0</Z>
						<R00>-1</R00>
						<R01>-0</R01>
						<R02>-0</R02>
						<R10>0</R10>
						<R11>0</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>1</R21>
						<R22>0</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="Enabled">true</bool>
					<float name="MaxVelocity">0.100000001</float>
					<string name="Name">Neck</string>
					<Ref name="Part0">RBX4</Ref>
					<Ref name="Part1">RBX1</Ref>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX9A73DEAFFB6A494EA0A93112B0DE16F5">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyBackAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXB04F3E850D5F4DFFAB73482A52FD7D61">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>0</Y>
						<Z>-0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyFrontAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXD1D562B5293949D8A8CBFB7B33F805A7">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>-1</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftCollarAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXFD82F71602724A7A9C52644C990C12CD">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">NeckAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX2F7ED22FAA984C5FB04FBBF4EE536C7A">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>1</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightCollarAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBX71C0B099A4594688901EA19902B3AC83">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistBackAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXA6E7FE6C0A4D466DA1A35811C96422DE">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistCenterAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
			<Item class="Attachment" referent="RBXA91867B1671D44568A3B75760DD7D7F2">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-1</Y>
						<Z>-0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistFrontAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX8">
			<Properties>
				<bool name="Anchored">false</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>1.5</X>
					<Y>3</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="EnableFluidForces">false</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Left Arm</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXE4E81F569F154780BC751374C86A08CB">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftShoulderAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX6">
			<Properties>
				<bool name="Anchored">false</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>-1.5</X>
					<Y>3</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="EnableFluidForces">false</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Right Arm</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX3F3BCD6C9C714F93B5AEDCA160B93B55">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>1</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightShoulderAttachment</string>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX12">
			<Properties>
				<bool name="Anchored">false</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>0.5</X>
					<Y>1</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="EnableFluidForces">false</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Left Leg</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
		</Item>
		<Item class="Part" referent="RBX10">
			<Properties>
				<bool name="Anchored">false</bool>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>-0.5</X>
					<Y>1</Y>
					<Z>-0.5</Z>
					<R00>-1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>-1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<bool name="CanQuery">true</bool>
				<bool name="CanTouch">true</bool>
				<bool name="CastShadow">true</bool>
				<string name="CollisionGroup">Default</string>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<bool name="EnableFluidForces">false</bool>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">true</bool>
				<bool name="Massless">false</bool>
				<token name="Material">256</token>
				<string name="MaterialVariantSerialized"></string>
				<string name="Name">Right Leg</string>
				<CoordinateFrame name="PivotOffset">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<int name="RootPriority">0</int>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">0</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>1</X>
					<Y>2</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
		</Item>
		<Item class="Humanoid" referent="RBX14">
			<Properties>
				<BinaryString name="AttributesSerialize"></BinaryString>
				<bool name="AutoJumpEnabled">true</bool>
				<bool name="AutoRotate">true</bool>
				<bool name="AutomaticScalingEnabled">true</bool>
				<bool name="BreakJointsOnDeath">true</bool>
				<token name="CollisionType">0</token>
				<token name="DisplayDistanceType">0</token>
				<string name="DisplayName"></string>
				<bool name="EvaluateStateMachine">true</bool>
				<float name="HealthDisplayDistance">100</float>
				<token name="HealthDisplayType">0</token>
				<float name="Health_XML">100</float>
				<float name="HipHeight">0</float>
				<Vector3 name="InternalBodyScale">
					<X>1</X>
					<Y>1</Y>
					<Z>1</Z>
				</Vector3>
				<float name="InternalHeadScale">1</float>
				<float name="JumpHeight">7.19999981</float>
				<float name="JumpPower">50</float>
				<float name="MaxHealth">100</float>
				<float name="MaxSlopeAngle">89</float>
				<string name="Name">Humanoid</string>
				<float name="NameDisplayDistance">100</float>
				<token name="NameOcclusion">2</token>
				<bool name="RequiresNeck">true</bool>
				<token name="RigType">0</token>
				<int64 name="SourceAssetId">-1</int64>
				<BinaryString name="Tags"></BinaryString>
				<bool name="UseJumpPower">true</bool>
				<float name="WalkSpeed">16</float>
			</Properties>
			<Item class="Animator" referent="RBX9F1F4374CD124F46BCD30D56382C40D8">
				<Properties>
					<BinaryString name="AttributesSerialize"></BinaryString>
					<string name="Name">Animator</string>
					<bool name="PreferLodEnabled">true</bool>
					<int64 name="SourceAssetId">-1</int64>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
		</Item>
	</Item>
	<SharedStrings>
		<SharedString md5="yuZpQdnvvUBOTYh1jqZ2cA=="></SharedString>
	</SharedStrings>
</roblox>