using Newtonsoft.Json;
using System.Diagnostics;
using System.Net.Http;
using System.Text;

namespace RobloxLauncher;

public class UpdateManager : IDisposable
{
    private const string RobloxExecutableName = "RobloxPlayerBeta.exe";
    private const string UnknownVersion = "unknown";
    private const string VersionFileName = "version.txt";
    private readonly string _robloxPath;
    private readonly string _dataPath;
    private readonly HttpClient _httpClient;
    private readonly Settings _settings;

    public UpdateManager(string robloxPath, string cachePath, Settings settings)
    {
        _robloxPath = robloxPath;
        _dataPath = Path.Combine(Path.GetDirectoryName(robloxPath)!, "Data");
        _settings = settings;
        _httpClient = new HttpClient();
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "Roblox/WinInet");
    }

    public async Task<bool> CheckForUpdatesAsync()
    {
        try
        {
            var currentVersion = GetCurrentVersion();
            var latestVersion = await GetLatestVersionAsync();

            LogVersionInfo(currentVersion, latestVersion);

            if (string.IsNullOrEmpty(latestVersion))
            {
                Console.WriteLine("❌ Failed to get latest version");
                return false;
            }

            // Handle unknown version case
            if (await HandleUnknownVersionAsync(currentVersion))
                return false;

            // Check if update is needed
            return CheckIfUpdateNeeded(currentVersion, latestVersion);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Update check failed: {ex.Message}");
            return false;
        }
    }

    private static void LogVersionInfo(string currentVersion, string? latestVersion)
    {
        Console.WriteLine($"🔍 Current version: {currentVersion}");
        Console.WriteLine($"🔍 Latest version: {latestVersion}");
    }

    private Task<bool> HandleUnknownVersionAsync(string currentVersion)
    {
        if (currentVersion != UnknownVersion)
            return Task.FromResult(false);

        Console.WriteLine("⚠️ Current version unknown, checking if Roblox is working...");

        if (!IsRobloxWorkingProperly())
        {
            Console.WriteLine("❌ Roblox is not working properly, update may be needed");
            return Task.FromResult(false);
        }

        // Check if we should skip update when Roblox is working
        if (_settings.Roblox.SkipUpdateIfWorking)
        {
            Console.WriteLine("✅ Roblox is working properly, skipping unnecessary update (SkipUpdateIfWorking=true)");
            UpdateVersionInSettings();
            return Task.FromResult(true);
        }
        else
        {
            Console.WriteLine("⚠️ Roblox is working but SkipUpdateIfWorking=false, allowing update check to continue");
            return Task.FromResult(false);
        }
    }

    private void UpdateVersionInSettings()
    {
        try
        {
            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);
            var versionInfo = FileVersionInfo.GetVersionInfo(robloxExe);
            var fileVersion = versionInfo.FileVersion;
            if (!string.IsNullOrEmpty(fileVersion))
            {
                var robloxVersion = ConvertToRobloxVersionFormat(fileVersion);
                _settings.Roblox.Version = robloxVersion;
                Console.WriteLine($"📝 Updated version in settings: {robloxVersion}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"⚠️ Could not update version in settings: {ex.Message}");
        }
    }

    private bool CheckIfUpdateNeeded(string currentVersion, string latestVersion)
    {
        if (IsVersionUpToDate(currentVersion, latestVersion))
        {
            Console.WriteLine($"✅ Roblox is up to date: {currentVersion}");
            return false;
        }

        Console.WriteLine($"🔄 Update available: {currentVersion} → {latestVersion}");
        LogVersionComparisonDetails(currentVersion, latestVersion);
        LogPotentialFalsePositive(currentVersion);

        return true;
    }

    private static void LogVersionComparisonDetails(string currentVersion, string latestVersion)
    {
        Console.WriteLine($"🔍 Version comparison details:");
        Console.WriteLine($"   - Current: '{currentVersion}' (Length: {currentVersion.Length})");
        Console.WriteLine($"   - Latest: '{latestVersion}' (Length: {latestVersion.Length})");
        Console.WriteLine($"   - Are equal: {currentVersion == latestVersion}");
        Console.WriteLine($"   - IsVersionUpToDate result: {IsVersionUpToDate(currentVersion, latestVersion)}");
    }

    private void LogPotentialFalsePositive(string currentVersion)
    {
        if (currentVersion == UnknownVersion && IsRobloxWorkingProperly())
        {
            Console.WriteLine("⚠️ WARNING: This might be a false positive!");
            Console.WriteLine("   - Current version is unknown but Roblox is working");
            Console.WriteLine("   - Consider if update is really necessary");
        }
    }

    public async Task<bool> UpdateRobloxAsync(IProgress<string>? progress = null)
    {
        try
        {
            var (isUpdateNeeded, latestVersion) = await CheckUpdateRequiredAsync(progress);
            if (!isUpdateNeeded)
                return latestVersion != null; // true if up to date, false if error

            return await PerformUpdateWithBackupAsync(latestVersion!, progress);
        }
        catch (Exception ex)
        {
            progress?.Report($"❌ Update error: {ex.Message}");
            return false;
        }
    }

    private async Task<(bool isUpdateNeeded, string? latestVersion)> CheckUpdateRequiredAsync(IProgress<string>? progress)
    {
        progress?.Report("🔍 Checking for updates...");

        var latestVersion = await GetLatestVersionAsync();
        if (string.IsNullOrEmpty(latestVersion))
        {
            progress?.Report("❌ Failed to get latest version");
            return (false, null);
        }

        var currentVersion = GetCurrentVersion();

        // Log versions for debugging
        Console.WriteLine($"Current version: {currentVersion}");
        Console.WriteLine($"Latest version: {latestVersion}");

        // Compare versions
        if (IsVersionUpToDate(currentVersion, latestVersion))
        {
            progress?.Report("✅ Already up to date");
            return (false, latestVersion);
        }

        progress?.Report($"📥 Downloading Roblox {latestVersion}...");
        return (true, latestVersion);
    }

    private static bool IsVersionUpToDate(string currentVersion, string latestVersion)
    {
        // If versions are exactly the same
        if (currentVersion == latestVersion)
            return true;

        // If current version is unknown, assume update is needed
        if (currentVersion == UnknownVersion)
            return false;

        // Handle different version formats
        if (IsVersionHashFormat(currentVersion, latestVersion))
            return CompareVersionHashes(currentVersion, latestVersion);

        if (IsNumericVersionFormat(currentVersion, latestVersion))
            return CompareNumericVersions(currentVersion, latestVersion);

        // Default to string comparison
        return currentVersion == latestVersion;
    }

    private static bool IsVersionHashFormat(string currentVersion, string latestVersion)
    {
        return currentVersion.StartsWith("version-") && latestVersion.StartsWith("version-");
    }

    private static bool CompareVersionHashes(string currentVersion, string latestVersion)
    {
        var currentHash = currentVersion.Substring(8);
        var latestHash = latestVersion.Substring(8);
        return currentHash == latestHash;
    }

    private static bool IsNumericVersionFormat(string currentVersion, string latestVersion)
    {
        return currentVersion.Contains(".") && latestVersion.Contains(".");
    }

    private static bool CompareNumericVersions(string currentVersion, string latestVersion)
    {
        try
        {
            var currentParts = currentVersion.Split('.');
            var latestParts = latestVersion.Split('.');

            // Compare major parts
            for (int i = 0; i < Math.Min(currentParts.Length, latestParts.Length); i++)
            {
                if (int.TryParse(currentParts[i], out int currentPart) &&
                    int.TryParse(latestParts[i], out int latestPart))
                {
                    if (currentPart < latestPart)
                        return false;
                    if (currentPart > latestPart)
                        return true;
                }
            }

            // If all parts match but latest has more parts, current is older
            return currentParts.Length >= latestParts.Length;
        }
        catch
        {
            // If parsing fails, fall back to string comparison
            return currentVersion == latestVersion;
        }
    }

    private async Task<bool> PerformUpdateWithBackupAsync(string latestVersion, IProgress<string>? progress)
    {
        var backupPath = _robloxPath + "_backup";

        try
        {
            CreateBackup(backupPath, progress);
            var success = await DownloadAndInstallRobloxAsync(progress);

            if (success)
            {
                CompleteSuccessfulUpdate(latestVersion, backupPath, progress);
                return true;
            }
            else
            {
                RestoreBackup(backupPath, progress, "❌ Update failed, restored backup");
                return false;
            }
        }
        catch (Exception ex)
        {
            RestoreBackup(backupPath, progress, $"❌ Update failed: {ex.Message}");
            return false;
        }
    }

    private void CreateBackup(string backupPath, IProgress<string>? progress)
    {
        if (Directory.Exists(_robloxPath))
        {
            progress?.Report("💾 Creating backup...");
            if (Directory.Exists(backupPath))
                Directory.Delete(backupPath, true);
            Directory.Move(_robloxPath, backupPath);
        }
    }

    private void CompleteSuccessfulUpdate(string latestVersion, string backupPath, IProgress<string>? progress)
    {
        progress?.Report("💾 Saving version information...");

        // Update version in settings
        _settings.Roblox.Version = latestVersion;
        Console.WriteLine($"📝 Updated settings version to: {latestVersion}");

        // Also write version to version.txt for future reference
        try
        {
            var versionFile = Path.Combine(_robloxPath, VersionFileName);
            File.WriteAllText(versionFile, latestVersion);
            Console.WriteLine($"📝 Wrote version file: {versionFile}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Failed to write version file: {ex.Message}");
            progress?.Report($"⚠️ Warning: Could not save version file");
        }

        // Save settings to disk (this is crucial!)
        try
        {
            SaveSettingsToDisk();
            Console.WriteLine("📝 Settings saved to disk");
            progress?.Report("💾 Version information saved");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Failed to save settings: {ex.Message}");
            progress?.Report($"⚠️ Warning: Could not save settings");
        }

        if (Directory.Exists(backupPath))
            Directory.Delete(backupPath, true);

        progress?.Report($"✅ Successfully updated to {latestVersion}");
    }

    private void SaveSettingsToDisk()
    {
        try
        {
            var settingsPath = Path.Combine(_dataPath, "settings.json");
            var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
            File.WriteAllText(settingsPath, json);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Failed to save settings to disk: {ex.Message}");
            throw;
        }
    }

    private void RestoreBackup(string backupPath, IProgress<string>? progress, string message)
    {
        if (Directory.Exists(_robloxPath))
            Directory.Delete(_robloxPath, true);
        if (Directory.Exists(backupPath))
            Directory.Move(backupPath, _robloxPath);

        progress?.Report(message);
    }

    private async Task<string?> GetLatestVersionAsync()
    {
        try
        {
            var response = await _httpClient.GetStringAsync(_settings.Roblox.UpdateCheckUrl);
            var versionInfo = JsonConvert.DeserializeObject<dynamic>(response);

            // Get both version formats for better comparison
            var clientVersionUpload = versionInfo?.clientVersionUpload?.ToString();
            var version = versionInfo?.version?.ToString();

            // Prefer clientVersionUpload as it's more specific
            return !string.IsNullOrEmpty(clientVersionUpload) ? clientVersionUpload : version;
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Failed to get latest version: {ex.Message}");

            // Fallback: try to get version from system installation
            return GetSystemRobloxVersion();
        }
    }

    private string GetCurrentVersion()
    {
        try
        {
            // First check our saved version
            if (!string.IsNullOrEmpty(_settings.Roblox.Version))
                return _settings.Roblox.Version;

            // Check version file
            var versionFile = Path.Combine(_robloxPath, VersionFileName);
            if (File.Exists(versionFile))
            {
                var version = File.ReadAllText(versionFile).Trim();
                if (!string.IsNullOrEmpty(version))
                {
                    _settings.Roblox.Version = version;
                    return version;
                }
            }

            // Try to get version from executable
            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);
            if (File.Exists(robloxExe))
            {
                var versionInfo = FileVersionInfo.GetVersionInfo(robloxExe);
                var fileVersion = versionInfo.FileVersion;
                if (!string.IsNullOrEmpty(fileVersion))
                {
                    // Convert file version to Roblox format if needed
                    var robloxVersion = ConvertToRobloxVersionFormat(fileVersion);
                    _settings.Roblox.Version = robloxVersion;
                    return robloxVersion;
                }
            }

            // Try to get from system installation
            var systemVersion = GetSystemRobloxVersion();
            if (!string.IsNullOrEmpty(systemVersion))
            {
                _settings.Roblox.Version = systemVersion;
                return systemVersion;
            }

            return UnknownVersion;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to get current version: {ex.Message}");
            return UnknownVersion;
        }
    }

    private static string ConvertToRobloxVersionFormat(string fileVersion)
    {
        try
        {
            // Convert file version like "4.681.0.6810806" to Roblox format
            var parts = fileVersion.Split('.');
            if (parts.Length >= 4)
            {
                // Create version-hash format like "version-765338e04cf54fde"
                var versionNumber = $"{parts[1]}.{parts[2]}.{parts[3]}";
                var hash = GenerateVersionHash(versionNumber);
                return $"version-{hash}";
            }

            return fileVersion;
        }
        catch
        {
            return fileVersion;
        }
    }

    private static string GenerateVersionHash(string version)
    {
        // Generate a simple hash for version identification
        var hash = version.GetHashCode().ToString("x8");
        return hash.PadLeft(16, '0')[..16];
    }

    private static string? GetSystemRobloxVersion()
    {
        try
        {
            var systemRobloxPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "Roblox", "Versions");

            if (!Directory.Exists(systemRobloxPath))
                return null;

            var versionDirs = Directory.GetDirectories(systemRobloxPath)
                .OrderByDescending(d => Directory.GetLastWriteTime(d))
                .ToArray();

            if (versionDirs.Length == 0)
                return null;

            var latestVersionDir = versionDirs[0];
            var versionName = Path.GetFileName(latestVersionDir);
            
            // Extract version from directory name (format: version-hash)
            var parts = versionName.Split('-');
            return parts.Length > 0 ? parts[0] : versionName;
        }
        catch
        {
            return null;
        }
    }

    private async Task<bool> DownloadAndInstallRobloxAsync(IProgress<string>? progress = null)
    {
        try
        {
            // First try to copy from system installation
            var systemVersion = await CopyFromSystemInstallationAsync(progress);
            if (systemVersion)
                return true;

            // Fallback: download from Roblox
            return await DownloadFromRobloxAsync(progress);
        }
        catch (Exception ex)
        {
            progress?.Report($"❌ Installation failed: {ex.Message}");
            return false;
        }
    }

    private async Task<bool> CopyFromSystemInstallationAsync(IProgress<string>? progress = null)
    {
        try
        {
            var systemRobloxPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData),
                "Roblox", "Versions");

            if (!Directory.Exists(systemRobloxPath))
            {
                progress?.Report("📂 System Roblox directory not found");
                progress?.Report($"📂 Looking for: {systemRobloxPath}");
                return false;
            }

            progress?.Report("📂 Copying from system installation...");

            var versionDirs = Directory.GetDirectories(systemRobloxPath)
                .OrderByDescending(d => Directory.GetLastWriteTime(d))
                .ToArray();

            progress?.Report($"📂 Found {versionDirs.Length} version directories");

            if (versionDirs.Length == 0)
            {
                progress?.Report("📂 No version directories found in system Roblox");
                return false;
            }

            var latestVersion = versionDirs[0];
            var versionName = Path.GetFileName(latestVersion);
            progress?.Report($"📂 Using latest version: {versionName}");

            var robloxExe = Path.Combine(latestVersion, RobloxExecutableName);

            if (!File.Exists(robloxExe))
            {
                progress?.Report($"📂 {RobloxExecutableName} not found in {versionName}");
                return false;
            }

            Directory.CreateDirectory(_robloxPath);

            // Copy all files
            await Task.Run(() => CopyDirectory(latestVersion, _robloxPath, true));

            // Save version info
            var versionFile = Path.Combine(_robloxPath, VersionFileName);
            await File.WriteAllTextAsync(versionFile, versionName);

            progress?.Report("✅ Copied from system installation");
            return true;
        }
        catch (Exception ex)
        {
            progress?.Report($"❌ System copy failed: {ex.Message}");
            return false;
        }
    }

    private async Task<bool> DownloadFromRobloxAsync(IProgress<string>? progress = null)
    {
        try
        {
            progress?.Report("📥 Downloading Roblox installer...");
            progress?.Report($"📥 Download URL: {_settings.Roblox.DownloadUrl}");

            var tempFile = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName() + ".exe");
            progress?.Report($"📥 Temp file: {tempFile}");

            using (var response = await _httpClient.GetAsync(_settings.Roblox.DownloadUrl))
            {
                progress?.Report($"📥 Response status: {response.StatusCode}");
                response.EnsureSuccessStatusCode();

                var contentLength = response.Content.Headers.ContentLength;
                progress?.Report($"📥 File size: {(contentLength ?? 0) / 1024 / 1024} MB");

                await using var fileStream = File.Create(tempFile);
                await response.Content.CopyToAsync(fileStream);
            }

            progress?.Report("🔧 Installing Roblox...");
            progress?.Report($"🔧 Running: {tempFile} /S");

            // Run installer silently
            var startInfo = new ProcessStartInfo
            {
                FileName = tempFile,
                Arguments = "/S",
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using var process = Process.Start(startInfo);
            if (process != null)
            {
                await process.WaitForExitAsync();
                progress?.Report($"🔧 Installer exit code: {process.ExitCode}");

                if (process.ExitCode == 0)
                {
                    progress?.Report("🔧 Installation completed, copying files...");
                    // Copy from newly installed system version
                    await Task.Delay(2000); // Wait for installation to complete
                    return await CopyFromSystemInstallationAsync(progress);
                }
                else
                {
                    progress?.Report($"❌ Installer failed with exit code: {process.ExitCode}");
                }
            }
            else
            {
                progress?.Report("❌ Failed to start installer process");
            }

            if (File.Exists(tempFile))
                File.Delete(tempFile);
            return false;
        }
        catch (Exception ex)
        {
            progress?.Report($"❌ Download failed: {ex.Message}");
            return false;
        }
    }

    public bool IsRobloxWorkingProperly()
    {
        try
        {
            if (!IsRobloxInstalled())
                return false;

            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);
            if (!File.Exists(robloxExe))
                return false;

            // Check if the executable is valid
            var fileInfo = new FileInfo(robloxExe);
            if (fileInfo.Length < 1024 * 1024) // Less than 1MB is suspicious
                return false;

            // Try to get version info
            try
            {
                var versionInfo = FileVersionInfo.GetVersionInfo(robloxExe);
                return !string.IsNullOrEmpty(versionInfo.FileVersion);
            }
            catch
            {
                return false;
            }
        }
        catch
        {
            return false;
        }
    }

    public async Task<string> DiagnoseUpdateSystemAsync()
    {
        var report = new StringBuilder();
        report.AppendLine("🔍 COMPREHENSIVE UPDATE SYSTEM DIAGNOSIS");
        report.AppendLine("=" + new string('=', 50));

        try
        {
            // 1. Check settings
            report.AppendLine("\n1️⃣ SETTINGS ANALYSIS:");
            report.AppendLine($"   • AutoUpdate: {_settings.Roblox.AutoUpdate}");
            report.AppendLine($"   • CheckOnStartup: {_settings.Roblox.CheckOnStartup}");
            report.AppendLine($"   • SkipUpdateIfWorking: {_settings.Roblox.SkipUpdateIfWorking}");
            report.AppendLine($"   • Saved Version: '{_settings.Roblox.Version}'");
            report.AppendLine($"   • Update URL: {_settings.Roblox.UpdateCheckUrl}");

            // 2. Check current version detection
            report.AppendLine("\n2️⃣ CURRENT VERSION DETECTION:");
            var currentVersion = GetCurrentVersion();
            report.AppendLine($"   • Final Result: '{currentVersion}'");

            // Check each source
            report.AppendLine("   • Sources checked:");
            report.AppendLine($"     - Settings: '{_settings.Roblox.Version}'");

            var versionFile = Path.Combine(_robloxPath, VersionFileName);
            if (File.Exists(versionFile))
            {
                var fileContent = await File.ReadAllTextAsync(versionFile);
                report.AppendLine($"     - {VersionFileName}: '{fileContent.Trim()}'");
            }
            else
            {
                report.AppendLine($"     - version.txt: NOT FOUND");
            }

            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);
            if (File.Exists(robloxExe))
            {
                try
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(robloxExe);
                    report.AppendLine($"     - Executable: '{versionInfo.FileVersion}'");
                    report.AppendLine($"     - Product Version: '{versionInfo.ProductVersion}'");
                    report.AppendLine($"     - File Size: {new FileInfo(robloxExe).Length / 1024 / 1024} MB");
                }
                catch (Exception ex)
                {
                    report.AppendLine($"     - Executable: ERROR - {ex.Message}");
                }
            }
            else
            {
                report.AppendLine($"     - Executable: NOT FOUND at {robloxExe}");
            }

            var systemVersion = GetSystemRobloxVersion();
            report.AppendLine($"     - System: '{systemVersion}'");

            // 3. Check latest version from API
            report.AppendLine("\n3️⃣ LATEST VERSION FROM API:");
            try
            {
                var latestVersion = await GetLatestVersionAsync();
                report.AppendLine($"   • API Response: '{latestVersion}'");

                if (!string.IsNullOrEmpty(latestVersion))
                {
                    // Test the API call manually
                    using var client = new HttpClient();
                    client.DefaultRequestHeaders.Add("User-Agent", "Roblox/WinInet");
                    var response = await client.GetStringAsync(_settings.Roblox.UpdateCheckUrl);
                    report.AppendLine($"   • Raw API Response: {response.Substring(0, Math.Min(200, response.Length))}...");
                }
            }
            catch (Exception ex)
            {
                report.AppendLine($"   • API ERROR: {ex.Message}");
            }

            // 4. Version comparison logic
            report.AppendLine("\n4️⃣ VERSION COMPARISON:");
            var latest = await GetLatestVersionAsync();
            if (!string.IsNullOrEmpty(latest))
            {
                report.AppendLine($"   • Current: '{currentVersion}' (Length: {currentVersion.Length})");
                report.AppendLine($"   • Latest: '{latest}' (Length: {latest.Length})");
                report.AppendLine($"   • Are Equal: {currentVersion == latest}");
                report.AppendLine($"   • IsVersionUpToDate: {IsVersionUpToDate(currentVersion, latest)}");

                if (currentVersion == UnknownVersion)
                {
                    report.AppendLine($"   • Unknown Version Handling:");
                    report.AppendLine($"     - IsRobloxWorkingProperly: {IsRobloxWorkingProperly()}");
                    report.AppendLine($"     - SkipUpdateIfWorking: {_settings.Roblox.SkipUpdateIfWorking}");
                }
            }

            // 5. File system check
            report.AppendLine("\n5️⃣ FILE SYSTEM CHECK:");
            report.AppendLine($"   • Roblox Path: {_robloxPath}");
            report.AppendLine($"   • Path Exists: {Directory.Exists(_robloxPath)}");
            if (Directory.Exists(_robloxPath))
            {
                var files = Directory.GetFiles(_robloxPath, "*.*", SearchOption.TopDirectoryOnly);
                report.AppendLine($"   • Files Count: {files.Length}");
                report.AppendLine($"   • Key Files:");
                foreach (var file in files.Take(10))
                {
                    var fileName = Path.GetFileName(file);
                    var size = new FileInfo(file).Length / 1024;
                    report.AppendLine($"     - {fileName} ({size} KB)");
                }
            }

            // 6. Final recommendation
            report.AppendLine("\n6️⃣ DIAGNOSIS RESULT:");
            var shouldUpdate = !IsVersionUpToDate(currentVersion, latest ?? "");
            report.AppendLine($"   • Should Update: {shouldUpdate}");

            if (shouldUpdate && currentVersion == UnknownVersion && IsRobloxWorkingProperly())
            {
                report.AppendLine($"   • ⚠️ WARNING: This appears to be a FALSE POSITIVE!");
                report.AppendLine($"   • Roblox is working but version is unknown");
                report.AppendLine($"   • Recommendation: Skip update or fix version detection");
            }
            else if (shouldUpdate)
            {
                report.AppendLine($"   • ✅ This appears to be a LEGITIMATE UPDATE");
            }
            else
            {
                report.AppendLine($"   • ✅ No update needed - versions match");
            }

        }
        catch (Exception ex)
        {
            report.AppendLine($"\n❌ DIAGNOSIS ERROR: {ex.Message}");
            report.AppendLine($"Stack Trace: {ex.StackTrace}");
        }

        report.AppendLine("\n" + new string('=', 60));
        return report.ToString();
    }

    public async Task<bool> FixVersionDetectionAsync()
    {
        try
        {
            Console.WriteLine("🔧 Attempting to fix version detection...");

            // Try to get version from executable
            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);
            if (File.Exists(robloxExe))
            {
                try
                {
                    var versionInfo = FileVersionInfo.GetVersionInfo(robloxExe);
                    var fileVersion = versionInfo.FileVersion;
                    if (!string.IsNullOrEmpty(fileVersion))
                    {
                        var robloxVersion = ConvertToRobloxVersionFormat(fileVersion);

                        // Save to settings
                        _settings.Roblox.Version = robloxVersion;

                        // Save to version file
                        var versionFile = Path.Combine(_robloxPath, VersionFileName);
                        await File.WriteAllTextAsync(versionFile, robloxVersion);

                        // Save settings to disk
                        SaveSettingsToDisk();

                        Console.WriteLine($"✅ Fixed version detection: {robloxVersion}");
                        return true;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"❌ Failed to read executable version: {ex.Message}");
                }
            }

            // Try to get from system installation
            var systemVersion = GetSystemRobloxVersion();
            if (!string.IsNullOrEmpty(systemVersion) && systemVersion != UnknownVersion)
            {
                _settings.Roblox.Version = systemVersion;

                var versionFile = Path.Combine(_robloxPath, VersionFileName);
                await File.WriteAllTextAsync(versionFile, systemVersion);

                // Save settings to disk
                SaveSettingsToDisk();

                Console.WriteLine($"✅ Fixed version from system: {systemVersion}");
                return true;
            }

            // Try to get latest version and assume it's current if Roblox is working
            if (IsRobloxWorkingProperly())
            {
                var latestVersion = await GetLatestVersionAsync();
                if (!string.IsNullOrEmpty(latestVersion))
                {
                    _settings.Roblox.Version = latestVersion;

                    var versionFile = Path.Combine(_robloxPath, VersionFileName);
                    await File.WriteAllTextAsync(versionFile, latestVersion);

                    // Save settings to disk
                    SaveSettingsToDisk();

                    Console.WriteLine($"✅ Assumed current version from latest: {latestVersion}");
                    return true;
                }
            }

            Console.WriteLine("❌ Could not fix version detection");
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Version fix failed: {ex.Message}");
            return false;
        }
    }

    public async Task<string?> GetLatestVersionPublicAsync()
    {
        return await GetLatestVersionAsync();
    }

    public string GetCurrentVersionPublic()
    {
        return GetCurrentVersion();
    }

    public bool IsRobloxInstalled()
    {
        try
        {
            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);
            return File.Exists(robloxExe);
        }
        catch
        {
            return false;
        }
    }

    public string GetInstalledVersionInfo()
    {
        try
        {
            var currentVersion = GetCurrentVersion();
            var robloxExe = Path.Combine(_robloxPath, RobloxExecutableName);

            if (File.Exists(robloxExe))
            {
                var fileInfo = new FileInfo(robloxExe);
                var versionInfo = FileVersionInfo.GetVersionInfo(robloxExe);

                return $"Version: {currentVersion}\n" +
                       $"File Version: {versionInfo.FileVersion}\n" +
                       $"Product Version: {versionInfo.ProductVersion}\n" +
                       $"File Size: {fileInfo.Length / 1024 / 1024} MB\n" +
                       $"Last Modified: {fileInfo.LastWriteTime}";
            }

            return "Roblox not installed";
        }
        catch (Exception ex)
        {
            return $"Error getting version info: {ex.Message}";
        }
    }

    public async Task<string> TestUpdateSystemAsync()
    {
        var result = new StringBuilder();
        result.AppendLine("🔍 Testing Update System...\n");

        try
        {
            // Test 1: Check API connectivity
            result.AppendLine("1. Testing API connectivity...");
            var latestVersion = await GetLatestVersionAsync();
            if (!string.IsNullOrEmpty(latestVersion))
            {
                result.AppendLine($"   ✅ API working - Latest version: {latestVersion}");
            }
            else
            {
                result.AppendLine("   ❌ API failed");
            }

            // Test 2: Check current version
            result.AppendLine("\n2. Checking current version...");
            var currentVersion = GetCurrentVersion();
            result.AppendLine($"   Current version: {currentVersion}");

            // Test 3: Check installation
            result.AppendLine("\n3. Checking installation...");
            if (IsRobloxInstalled())
            {
                result.AppendLine("   ✅ Roblox is installed");
                result.AppendLine($"   {GetInstalledVersionInfo()}");
            }
            else
            {
                result.AppendLine("   ❌ Roblox not installed");
            }

            // Test 4: Version comparison
            result.AppendLine("\n4. Testing version comparison...");
            if (!string.IsNullOrEmpty(latestVersion))
            {
                var isUpToDate = IsVersionUpToDate(currentVersion, latestVersion);
                result.AppendLine($"   Up to date: {(isUpToDate ? "✅ Yes" : "❌ No")}");
            }

            // Test 5: Check system installation
            result.AppendLine("\n5. Checking system Roblox...");
            var systemVersion = GetSystemRobloxVersion();
            if (!string.IsNullOrEmpty(systemVersion))
            {
                result.AppendLine($"   ✅ System Roblox found: {systemVersion}");
            }
            else
            {
                result.AppendLine("   ❌ System Roblox not found");
            }

            result.AppendLine("\n✅ Update system test completed!");
        }
        catch (Exception ex)
        {
            result.AppendLine($"\n❌ Test failed: {ex.Message}");
        }

        return result.ToString();
    }

    private static void CopyDirectory(string sourceDir, string destinationDir, bool recursive)
    {
        var dir = new DirectoryInfo(sourceDir);
        if (!dir.Exists)
            throw new DirectoryNotFoundException($"Source directory not found: {dir.FullName}");

        DirectoryInfo[] dirs = dir.GetDirectories();
        Directory.CreateDirectory(destinationDir);

        foreach (FileInfo file in dir.GetFiles())
        {
            string targetFilePath = Path.Combine(destinationDir, file.Name);
            file.CopyTo(targetFilePath, true);
        }

        if (recursive)
        {
            foreach (DirectoryInfo subDir in dirs)
            {
                string newDestinationDir = Path.Combine(destinationDir, subDir.Name);
                CopyDirectory(subDir.FullName, newDestinationDir, true);
            }
        }
    }

    private bool _disposed = false;

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                _httpClient?.Dispose();
            }
            _disposed = true;
        }
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
}
