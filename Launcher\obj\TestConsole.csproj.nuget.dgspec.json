{"format": 1, "restore": {"E:\\Users\\Administrator\\Downloads\\Compressed\\RobloxPortable\\Launcher\\TestConsole.csproj": {}}, "projects": {"E:\\Users\\Administrator\\Downloads\\Compressed\\RobloxPortable\\Launcher\\TestConsole.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Users\\Administrator\\Downloads\\Compressed\\RobloxPortable\\Launcher\\TestConsole.csproj", "projectName": "TestConsole", "projectPath": "E:\\Users\\Administrator\\Downloads\\Compressed\\RobloxPortable\\Launcher\\TestConsole.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Users\\Administrator\\Downloads\\Compressed\\RobloxPortable\\Launcher\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\6.0.428\\RuntimeIdentifierGraph.json"}}}}}