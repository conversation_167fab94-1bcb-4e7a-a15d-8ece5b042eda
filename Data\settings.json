{"version": "1.0.0", "portable": {"enabled": true, "baseDirectory": ".", "robloxPath": "<PERSON><PERSON><PERSON>", "cachePath": "<PERSON><PERSON>", "dataPath": "Data", "toolsPath": "Tools"}, "roblox": {"autoUpdate": false, "checkOnStartup": false, "updateNotification": true, "preserveCache": true, "skipUpdateIfWorking": true, "version": "0.681.0.6810806", "installPath": "<PERSON><PERSON><PERSON>", "userDataPath": "Cache/UserData", "logLevel": "Info", "updateCheckUrl": "https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer"}, "cache": {"enabled": true, "maxSizeGB": 50, "autoCleanup": false, "cleanupIntervalDays": 365, "compressionEnabled": true, "preloadCommonAssets": true, "permanentCaching": true, "cacheEverything": true, "aggressiveCaching": true, "instantCaching": true, "backgroundCaching": true, "cacheOnFirstAccess": true, "neverDeleteCache": true}, "launcher": {"theme": "Dark", "autoStart": false, "minimizeToTray": true, "checkUpdatesOnStart": true, "showAdvancedOptions": false}, "network": {"useProxy": false, "proxyAddress": "", "proxyPort": 0, "timeoutSeconds": 30, "retryAttempts": 3}, "logging": {"enabled": true, "level": "Info", "maxLogFiles": 10, "maxLogSizeMB": 10}}