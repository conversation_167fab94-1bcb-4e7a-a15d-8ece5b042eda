<roblox xmlns:xmime="http://www.w3.org/2005/05/xmlmime" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://www.roblox.com/roblox.xsd" version="4">
	<Meta name="ExplicitAutoJoints">false</Meta>
	<External>null</External>
	<External>nil</External>
	<Item class="Model" referent="RBXCA9064A7792D4A70B1BC7C6C29ABB45C">
		<Properties>
			<CoordinateFrame name="ModelInPrimary">
				<X>0</X>
				<Y>0</Y>
				<Z>0</Z>
				<R00>1</R00>
				<R01>0</R01>
				<R02>0</R02>
				<R10>0</R10>
				<R11>1</R11>
				<R12>0</R12>
				<R20>0</R20>
				<R21>0</R21>
				<R22>1</R22>
			</CoordinateFrame>
			<string name="Name">R15</string>
			<Ref name="PrimaryPart">RBX081D55DA3E944921A37ACF5DF5DC888A</Ref>
			<BinaryString name="Tags"></BinaryString>
		</Properties>
		<Item class="Part" referent="RBX081D55DA3E944921A37ACF5DF5DC888A">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">4</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.66999817</X>
					<Y>2.30299997</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<string name="Name">HumanoidRootPart</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">3</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">1</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">1</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>1.96000004</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX61A6868C869A42A2BA5A8872B870ECF0">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-0.342999995</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RootRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXC1125FD6AE7B417DAAFD5209C74E2152">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0</X>
							<Y>-0.349999994</Y>
							<Z>0</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Vector3Value" referent="RBXD9DCC0B3CA024E4291C8CE8D61413BC0">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>2</X>
						<Y>2</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX22C13C16C5FB47AABB4365E4AA62349F">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>6.16999769</X>
					<Y>2.10695553</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.999999762</X>
					<Y>0.300030679</Y>
					<Z>0.999999881</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715537</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715537</url></Content>
				<string name="Name">LeftHand</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.999999762</X>
					<Y>0.29403007</Y>
					<Z>0.999999881</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXA757090439CE46BE8148B6B7DE7DAF14">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.000478982925</X>
						<Y>0.122544497</Y>
						<Z>5.96046448e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftWristRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXBEB77C602E0A45CCA620F5E81B6C9179">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.000478982925</X>
							<Y>0.125045404</Y>
							<Z>5.96046448e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX5386D81C50564E6EA062F9D311EE7ECD">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-0.146955431</Y>
						<Z>-1.46306121e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>-0</R02>
						<R10>0</R10>
						<R11>6.12323426e-17</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>-1</R21>
						<R22>6.12323426e-17</R22>
					</CoordinateFrame>
					<string name="Name">LeftGripAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX598DF253DE8B454CBC326F43A1350EE6">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0</X>
							<Y>-0.149954513</Y>
							<Z>-1.46306121e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBXD8CFE6965DEA407ABA7F088B69488334">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0.000478625298</X>
						<Y>-0.490910143</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.000478982925</X>
						<Y>0.122544497</Y>
						<Z>5.96046448e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">LeftWrist</string>
					<Ref name="Part0">RBX3F40768EF729487C8794F9B4240FB7C4</Ref>
					<Ref name="Part1">RBX22C13C16C5FB47AABB4365E4AA62349F</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXFDDCF9A109E8486DB6BB89E71C918975">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.999999762</X>
						<Y>0.300030679</Y>
						<Z>0.999999881</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX3F40768EF729487C8794F9B4240FB7C4">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>6.16999817</X>
					<Y>2.72041011</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.999999642</X>
					<Y>1.05191803</Y>
					<Z>1</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715541</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715541</url></Content>
				<string name="Name">LeftLowerArm</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.999999642</X>
					<Y>1.03087974</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX04E0B730C04A4105BE7B4A7A91E9D8AE">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.000478625298</X>
						<Y>0.253514439</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftElbowRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX98A6D7D5D15045CA94AFD3F8964B4BAA">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.000478625298</X>
							<Y>0.258688211</Y>
							<Z>7.64462551e-20</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXD610AB40FF7044B4A764EC960E879D03">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.000478625298</X>
						<Y>-0.490910143</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftWristRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX93776E321180470CA732254BBE3E7041">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.000478625298</X>
							<Y>-0.5009287</Y>
							<Z>7.64462551e-20</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX45C74A680D184F1695B7F0C10CC787A1">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0.000479221344</X>
						<Y>-0.327375263</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.000478625298</X>
						<Y>0.253514439</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">LeftElbow</string>
					<Ref name="Part0">RBX65B95BED76AB4402A5C58B280CC87F73</Ref>
					<Ref name="Part1">RBX3F40768EF729487C8794F9B4240FB7C4</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXB2B8BD87E57F45B89CA5934B9C0DB21D">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.999999642</X>
						<Y>1.05191803</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX65B95BED76AB4402A5C58B280CC87F73">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>6.16999769</X>
					<Y>3.30129981</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.999999762</X>
					<Y>1.16867065</Y>
					<Z>0.99999994</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715550</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715550</url></Content>
				<string name="Name">LeftUpperArm</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.999999762</X>
					<Y>1.14529729</Y>
					<Z>0.99999994</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXEA299413338E484B8AFF55920DB9D553">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.500000358</X>
						<Y>0.386440158</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftShoulderRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXC7E628051CAF4EBF9E9AB78141897640">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.500000358</X>
							<Y>0.394326687</Y>
							<Z>8.94069672e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX2D6A6E77E79D4C38AFBB350D3FE20101">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.000479221344</X>
						<Y>-0.327375263</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftElbowRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX276F994BF71A4E3E83779B027292AAF0">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.000479221344</X>
							<Y>-0.334056377</Y>
							<Z>8.94069672e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXA4E5CC489D6A4EDBB602C959CED50784">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>2.38418579e-07</X>
						<Y>0.572640121</Y>
						<Z>-2.70968314e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftShoulderAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX23016E1EDBB04B578101FBB42DD4449F">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>2.38418579e-07</X>
							<Y>0.584326625</Y>
							<Z>-2.70968314e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBXE19B957B57DF41C6AF0A4C56CD445BC7">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-1</X>
						<Y>0.551756799</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>0.500000358</X>
						<Y>0.386440158</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">LeftShoulder</string>
					<Ref name="Part0">RBXA9943A145CA34B9199D5810AE7AC1AD5</Ref>
					<Ref name="Part1">RBX65B95BED76AB4402A5C58B280CC87F73</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX09675F8EB3834257BD339DF6E4BBE011">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.999999762</X>
						<Y>1.16867065</Y>
						<Z>0.99999994</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX138B2F31E7B64B03B104DB392895E6A2">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>9.16999817</X>
					<Y>2.10695553</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.999999881</X>
					<Y>0.300030679</Y>
					<Z>0.999999881</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715557</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715557</url></Content>
				<string name="Name">RightHand</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.999999881</X>
					<Y>0.29403007</Y>
					<Z>0.999999881</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX86F59DDE9AF14A97A2167A4992A0CA2C">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>3.57627869e-07</X>
						<Y>0.122544497</Y>
						<Z>5.96046448e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightWristRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXD52B8C849CCB4F3D9DD88C9D190A0DFA">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>3.57627869e-07</X>
							<Y>0.125045404</Y>
							<Z>5.96046448e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXAFDC6B9445D3448895DA17F17DDAE8CB">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0</X>
						<Y>-0.146955431</Y>
						<Z>-1.46306121e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>-0</R02>
						<R10>0</R10>
						<R11>6.12323426e-17</R11>
						<R12>1</R12>
						<R20>0</R20>
						<R21>-1</R21>
						<R22>6.12323426e-17</R22>
					</CoordinateFrame>
					<string name="Name">RightGripAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX8BAD50170A18462FA313452D934CBE7C">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0</X>
							<Y>-0.149954513</Y>
							<Z>-1.46306121e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX1BDF572B91FE42278E7E245DBCE7DB99">
				<Properties>
					<CoordinateFrame name="C0">
						<X>1.1920929e-07</X>
						<Y>-0.490910143</Y>
						<Z>-6.86244753e-18</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>3.57627869e-07</X>
						<Y>0.122544497</Y>
						<Z>5.96046448e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">RightWrist</string>
					<Ref name="Part0">RBX36AF3E43DBBF47F29C81B6C3A652EF28</Ref>
					<Ref name="Part1">RBX138B2F31E7B64B03B104DB392895E6A2</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXCEBB849D195B495E8C9667D7A7D12C54">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.999999881</X>
						<Y>0.300030679</Y>
						<Z>0.999999881</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX36AF3E43DBBF47F29C81B6C3A652EF28">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>9.16999817</X>
					<Y>2.72041011</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.999999762</X>
					<Y>1.05191803</Y>
					<Z>1</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715562</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715562</url></Content>
				<string name="Name">RightLowerArm</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.999999762</X>
					<Y>1.03087974</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXB0DE17F662D5431297CDB6716DAAF332">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>1.1920929e-07</X>
						<Y>0.253407896</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightElbowRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX8EA6CB01976641C28417A2E8294CA5EB">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>1.1920929e-07</X>
							<Y>0.258579493</Y>
							<Z>7.64462551e-20</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX056DEAAC60AA46E7B40E89C8AE2C31E6">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>1.1920929e-07</X>
						<Y>-0.490910143</Y>
						<Z>-6.86244753e-18</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightWristRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX8F90335A90164091A973ED097829E88F">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>1.1920929e-07</X>
							<Y>-0.5009287</Y>
							<Z>-6.86244753e-18</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBXF17BEE1D38FD48B2B6166C76294CEB02">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-5.96046448e-07</X>
						<Y>-0.327481806</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>1.1920929e-07</X>
						<Y>0.253407896</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">RightElbow</string>
					<Ref name="Part0">RBXE08AA1E71CF7447581F14072A70CB0ED</Ref>
					<Ref name="Part1">RBX36AF3E43DBBF47F29C81B6C3A652EF28</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX2A1AC1C92040409DB010DEDB8C569551">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.999999762</X>
						<Y>1.05191803</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBXE08AA1E71CF7447581F14072A70CB0ED">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>9.16999912</X>
					<Y>3.30129981</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.999999642</X>
					<Y>1.16867065</Y>
					<Z>0.99999994</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715576</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715576</url></Content>
				<string name="Name">RightUpperArm</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.999999642</X>
					<Y>1.14529729</Y>
					<Z>0.99999994</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX86E183B61C7543318A7E94B861BF10C8">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0.500000715</X>
						<Y>0.386440158</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightShoulderRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX2539207CDCC14ABE819FBD0C1ED549CC">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0.500000715</X>
							<Y>0.394326687</Y>
							<Z>8.94069672e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX65BA802C632246B8B75FD47A33D8EEE1">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-5.96046448e-07</X>
						<Y>-0.327481806</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightElbowRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXA74F953B99E04693BDA9564C061ABD1E">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-5.96046448e-07</X>
							<Y>-0.334165096</Y>
							<Z>8.94069672e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX198A6064CAA94F46AC59513CF7BDB7D5">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-8.34465027e-07</X>
						<Y>0.572640121</Y>
						<Z>-2.70968314e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightShoulderAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXBA0B3F85D0994AB088BE78F69B81C2A9">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-8.34465027e-07</X>
							<Y>0.584326625</Y>
							<Z>-2.70968314e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBXF66D83395529431481FEB7DBF5C94A57">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0.99999994</X>
						<Y>0.551756799</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0.500000715</X>
						<Y>0.386440158</Y>
						<Z>8.94069672e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">RightShoulder</string>
					<Ref name="Part0">RBXA9943A145CA34B9199D5810AE7AC1AD5</Ref>
					<Ref name="Part1">RBXE08AA1E71CF7447581F14072A70CB0ED</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX6F5798538BC94C52993A32103F0E358C">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.999999642</X>
						<Y>1.16867065</Y>
						<Z>0.99999994</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBXA9943A145CA34B9199D5810AE7AC1AD5">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.66999817</X>
					<Y>3.13598323</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>2</X>
					<Y>1.60003424</Y>
					<Z>1.00000036</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715593</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715593</url></Content>
				<string name="Name">UpperTorso</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>2</X>
					<Y>1.56803358</Y>
					<Z>1.00000036</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX6EC119D41F80483391EEE57C8AB1A728">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-5.96046448e-08</X>
						<Y>-0.783986032</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXA22C5A47BC43491787E34E8FA524131D">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-5.96046448e-08</X>
							<Y>-0.799985707</Y>
							<Z>1.1920929e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX6C3AAC7266EF462CA8E3577865544AED">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-5.96046448e-08</X>
						<Y>0.784016788</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">NeckRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXD4AFC1EF920C4CFB88D6346EF452B4F7">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-5.96046448e-08</X>
							<Y>0.800017118</Y>
							<Z>1.1920929e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX6F56EE2F02AD468A9B7B3E788437CB62">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1</X>
						<Y>0.551756799</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftShoulderRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX19C66964CD574ECAA1022F30F8BA2870">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1</X>
							<Y>0.56301713</Y>
							<Z>1.1920929e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX99ADEB41446443EBA925ABF6A770772F">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.99999994</X>
						<Y>0.551756799</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightShoulderRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXEC24FAEFBA504D99914664584047720B">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.99999994</X>
							<Y>0.56301713</Y>
							<Z>1.1920929e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXEF07CA1E8D974B4693A7B6D61E7D3B73">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-5.96046448e-08</X>
						<Y>-0.195985973</Y>
						<Z>-0.499999881</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyFrontAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXD9DB1E0FE94F406BB4C3B01D1CE44B44">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-5.96046448e-08</X>
							<Y>-0.199985683</Y>
							<Z>-0.499999881</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX1923AB9589C14F4CA895203B74DCDC4A">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-5.96046448e-08</X>
						<Y>-0.195985973</Y>
						<Z>0.5</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">BodyBackAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXAA61920CC77F4440B8169A57CB021D52">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-5.96046448e-08</X>
							<Y>-0.199985683</Y>
							<Z>0.5</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXCDEAB2EC26CC4FE19DE1FC115019C16A">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0.999999881</X>
						<Y>0.784016669</Y>
						<Z>-7.27397378e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftCollarAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX0EE4AB94321E45DEB4493989162E12BC">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0.999999881</X>
							<Y>0.800016999</Y>
							<Z>-7.27397378e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXA64DFB83DA004415AF017CDE97D679C5">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.99999994</X>
						<Y>0.78401643</Y>
						<Z>4.61295997e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightCollarAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXCBE1312350A549D89743170F910B45A6">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.99999994</X>
							<Y>0.800016761</Y>
							<Z>4.61295997e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX5B2CA333C0394C3C9072E34736D65D72">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-5.05859497e-08</X>
						<Y>0.784016788</Y>
						<Z>7.11172419e-08</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">NeckAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX97613F15C8FC4121AF9DDF25BAEFE380">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-5.05859497e-08</X>
							<Y>0.800017118</Y>
							<Z>7.11172419e-08</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX2A1FE8B023474F1D81C9E0DFF3CA8274">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-1.1920929e-07</X>
						<Y>0.196024418</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-5.96046448e-08</X>
						<Y>-0.783986032</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">Waist</string>
					<Ref name="Part0">RBXB8FFBCE768F34D8D8D966F48788528E0</Ref>
					<Ref name="Part1">RBXA9943A145CA34B9199D5810AE7AC1AD5</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX82D7F32542C246B19D237B0D820DE75E">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>2</X>
						<Y>1.60003424</Y>
						<Z>1.00000036</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX0FB02BF4485A49C89C7ABEF4D19DB3BE">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.16999817</X>
					<Y>0.147000074</Y>
					<Z>-6.00000095</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>1</X>
					<Y>0.300000191</Y>
					<Z>1</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715602</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715602</url></Content>
				<string name="Name">LeftFoot</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>1</X>
					<Y>0.294000179</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX82ECC85F593642A6A49B7C25B6A5F480">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1.78813934e-07</X>
						<Y>0.0999008864</Y>
						<Z>-1.7222776e-06</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftAnkleRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXCC5273F91C0143ED9CBDB457EF14D398">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1.78813934e-07</X>
							<Y>0.101939678</Y>
							<Z>-1.7222776e-06</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX1F501EA1B7A24A35875D27113CEF6021">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-1.1920929e-07</X>
						<Y>-0.536214054</Y>
						<Z>-2.21401592e-06</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-1.78813934e-07</X>
						<Y>0.0999008864</Y>
						<Z>-1.7222776e-06</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">LeftAnkle</string>
					<Ref name="Part0">RBX893C6CF7B2374480B69BBBE805825872</Ref>
					<Ref name="Part1">RBX0FB02BF4485A49C89C7ABEF4D19DB3BE</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX4A80EEB49E074ED7BD92CF3468D403AF">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>1</X>
						<Y>0.300000191</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX893C6CF7B2374480B69BBBE805825872">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.16999817</X>
					<Y>0.783115029</Y>
					<Z>-6.00000048</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.99999994</X>
					<Y>1.1930927</Y>
					<Z>0.999999523</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715610</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715610</url></Content>
				<string name="Name">LeftLowerLeg</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.99999994</X>
					<Y>1.16923082</Y>
					<Z>0.999999523</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXB35B673944584E63814325AE3E0BA69B">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>2.98023224e-08</X>
						<Y>0.371438056</Y>
						<Z>-1.60860594e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftKneeRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX096B5843B1214196A26B0CDB5335E6F6">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>2.98023224e-08</X>
							<Y>0.379018426</Y>
							<Z>-1.60860594e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX1C993A5F17634471A6D358BFA54EA204">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1.1920929e-07</X>
						<Y>-0.536214054</Y>
						<Z>-2.21401592e-06</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftAnkleRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXDC1D8830984E472ABD1DB73EB819446F">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1.1920929e-07</X>
							<Y>-0.547157168</Y>
							<Z>-2.21401592e-06</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX15B9FCA233BE4AE085F27CD44CA44B32">
				<Properties>
					<CoordinateFrame name="C0">
						<X>8.94069672e-08</X>
						<Y>-0.393080324</Y>
						<Z>-4.29081496e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>2.98023224e-08</X>
						<Y>0.371438056</Y>
						<Z>-1.60860594e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">LeftKnee</string>
					<Ref name="Part0">RBXAF9FA9B09EA04DFEA9D919064EDE7D6F</Ref>
					<Ref name="Part1">RBX893C6CF7B2374480B69BBBE805825872</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX9B70A257987C4E539D905AE15A94C22E">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.99999994</X>
						<Y>1.1930927</Y>
						<Z>0.999999523</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBXAF9FA9B09EA04DFEA9D919064EDE7D6F">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.16999817</X>
					<Y>1.54763341</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>1.00000036</X>
					<Y>1.21656859</Y>
					<Z>0.999999881</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715616</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715616</url></Content>
				<string name="Name">LeftUpperLeg</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>1.00000036</X>
					<Y>1.19223726</Y>
					<Z>0.999999881</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX25D21884116941DBB6119D868D65DA27">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>5.96046448e-08</X>
						<Y>0.412366509</Y>
						<Z>-1.63912773e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftHipRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX6DCE47E532D149CAA7D5F3BE72502709">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>5.96046448e-08</X>
							<Y>0.420782149</Y>
							<Z>-1.63912773e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXF56E9D581F724AC5877F5EBF923A9555">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>8.94069672e-08</X>
						<Y>-0.393080324</Y>
						<Z>-4.29081496e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftKneeRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX4D7BB4AF2DE7456A9EF0512B9B4ABF7D">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>8.94069672e-08</X>
							<Y>-0.401102364</Y>
							<Z>-4.29081496e-07</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX075E05BC6C674689B1E57FCD318DBA82">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-0.500000119</X>
						<Y>-0.195972815</Y>
						<Z>-0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>5.96046448e-08</X>
						<Y>0.412366509</Y>
						<Z>-1.63912773e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">LeftHip</string>
					<Ref name="Part0">RBXB8FFBCE768F34D8D8D966F48788528E0</Ref>
					<Ref name="Part1">RBXAF9FA9B09EA04DFEA9D919064EDE7D6F</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXABFE702DE3884CD6ABA97632335E8A7C">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>1.00000036</X>
						<Y>1.21656859</Y>
						<Z>0.999999881</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBXC1076273B95B402B92B9B47AC621CE81">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>8.16999817</X>
					<Y>0.147000074</Y>
					<Z>-5.99999952</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.99999994</X>
					<Y>0.300000191</Y>
					<Z>1</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715627</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715627</url></Content>
				<string name="Name">RightFoot</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.99999994</X>
					<Y>0.294000179</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX2C33B6908563404B8C6E98B75D44D75B">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0</X>
						<Y>0.0999007672</Y>
						<Z>7.64477954e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightAnkleRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXE9D01A3CDF354A94801A29C7A7DFC40E">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0</X>
							<Y>0.101939559</Y>
							<Z>7.64477954e-05</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBXB265FB312F7442A39062BD4D6C7F7EC0">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-0</X>
						<Y>-0.536214054</Y>
						<Z>7.62689815e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0</X>
						<Y>0.0999007672</Y>
						<Z>7.64477954e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">RightAnkle</string>
					<Ref name="Part0">RBX934B19001E8E48CD955C16029E83167A</Ref>
					<Ref name="Part1">RBXC1076273B95B402B92B9B47AC621CE81</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXBC532663919F4ACF9FD13DE590FA52B1">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.99999994</X>
						<Y>0.300000191</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX934B19001E8E48CD955C16029E83167A">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>8.16999817</X>
					<Y>0.78311491</Y>
					<Z>-5.99999952</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>0.99999994</X>
					<Y>1.19309282</Y>
					<Z>1.00000012</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715632</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715632</url></Content>
				<string name="Name">RightLowerLeg</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>0.99999994</X>
					<Y>1.16923094</Y>
					<Z>1.00000012</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBXD337F060DDBB4821917F4631E4DE5AEA">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0</X>
						<Y>0.371590823</Y>
						<Z>2.5553607e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightKneeRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXF75963E8411E44D2BD1AD444CF968D8D">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0</X>
							<Y>0.379174292</Y>
							<Z>2.5553607e-05</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXFFE32A5025214EE7B733E112AF84C50C">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0</X>
						<Y>-0.536214054</Y>
						<Z>7.62689815e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightAnkleRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX9649AA5F859940CEAC977B638EC83D80">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0</X>
							<Y>-0.547157168</Y>
							<Z>7.62689815e-05</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBXA6A3120B716C4041ABDFDD806715A9EF">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-0</X>
						<Y>-0.392927587</Y>
						<Z>-2.18767891e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0</X>
						<Y>0.371590823</Y>
						<Z>2.5553607e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">RightKnee</string>
					<Ref name="Part0">RBX1174DFBAABA34412840A7F27FF9DDCE9</Ref>
					<Ref name="Part1">RBX934B19001E8E48CD955C16029E83167A</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXD73D854CDC4D41FD92E5DF24E3AF9D97">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>0.99999994</X>
						<Y>1.19309282</Y>
						<Z>1.00000012</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBX1174DFBAABA34412840A7F27FF9DDCE9">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>8.16999817</X>
					<Y>1.54763329</Y>
					<Z>-5.99995232</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">false</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>1.00000048</X>
					<Y>1.21656859</Y>
					<Z>0.99996686</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715641</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715641</url></Content>
				<string name="Name">RightUpperLeg</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>1.00000048</X>
					<Y>1.19223726</Y>
					<Z>0.99996686</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX5F86F8EA013D4AA7BED1D974A2801E5B">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0</X>
						<Y>0.412366629</Y>
						<Z>-6.67300628e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightHipRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXEE8CC8FA7C8A4CFE9FD3335CC1E9E4C8">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0</X>
							<Y>0.420782268</Y>
							<Z>-6.67300628e-05</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX50E33947A2434C098B381E0D89F8EA79">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0</X>
						<Y>-0.392927587</Y>
						<Z>-2.18767891e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightKneeRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX8AE5275768DE4598AF798F0441A1DE1F">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0</X>
							<Y>-0.400946498</Y>
							<Z>-2.18767891e-05</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX57F7FB6EFFCA47E6BCD07F7538EEA8C2">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0.499999881</X>
						<Y>-0.195972815</Y>
						<Z>-1.91208565e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0</X>
						<Y>0.412366629</Y>
						<Z>-6.67300628e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">RightHip</string>
					<Ref name="Part0">RBXB8FFBCE768F34D8D8D966F48788528E0</Ref>
					<Ref name="Part1">RBX1174DFBAABA34412840A7F27FF9DDCE9</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX3AB823BA967D4B7A820CE432DFB1A18E">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>1.00000048</X>
						<Y>1.21656859</Y>
						<Z>0.99996686</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="MeshPart" referent="RBXB8FFBCE768F34D8D8D966F48788528E0">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.66999817</X>
					<Y>2.15597272</Y>
					<Z>-6</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<token name="CollisionFidelity">2</token>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<Vector3 name="InitialSize">
					<X>1.99999976</X>
					<Y>0.400055438</Y>
					<Z>1.00000012</Z>
				</Vector3>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<Content name="MeshID"><url>http://www.roblox.com/asset/?id=1699715652</url></Content>
				<Content name="MeshId"><url>http://www.roblox.com/asset/?id=1699715652</url></Content>
				<string name="Name">LowerTorso</string>
				<BinaryString name="PhysicsData"></BinaryString>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<Content name="TextureID"><null></null></Content>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<Vector3 name="size">
					<X>1.99999976</X>
					<Y>0.392054349</Y>
					<Z>1.00000012</Z>
				</Vector3>
			</Properties>
			<Item class="Attachment" referent="RBX7EB4B7271BFA47FDB9B3D319CA95C1A7">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1.1920929e-07</X>
						<Y>-0.195972815</Y>
						<Z>-0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RootRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX8AF29FB0ECA94DD6A54BFE235216D809">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1.1920929e-07</X>
							<Y>-0.199972257</Y>
							<Z>-0</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXE3DA97A56D5849CCB45E5F3D7F7CFA76">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1.1920929e-07</X>
						<Y>0.196024418</Y>
						<Z>7.64462551e-20</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX4E41F62CA75F4761AD85D38DAAEDF465">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1.1920929e-07</X>
							<Y>0.200024918</Y>
							<Z>7.64462551e-20</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXB5542454406E47CE9DD6FA7F60C07477">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0.500000119</X>
						<Y>-0.195972815</Y>
						<Z>-0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">LeftHipRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXAD66D3E57F9E4361B51FA14C36194D41">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0.500000119</X>
							<Y>-0.199972257</Y>
							<Z>-0</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXE441EF195BFF4F55BABEED6BAF0FD46E">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>0.499999881</X>
						<Y>-0.195972815</Y>
						<Z>-1.91208565e-05</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">RightHipRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXC6B857E6895F420DBEA5A8F4AA6957B6">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>0.499999881</X>
							<Y>-0.199972257</Y>
							<Z>-1.91208565e-05</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX990B764C553E4723875822351D757518">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-4.2200088e-07</X>
						<Y>-0.195972815</Y>
						<Z>-1.65436123e-24</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistCenterAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXFD65064FE634464A9881925937A64A73">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-4.2200088e-07</X>
							<Y>-0.199972257</Y>
							<Z>-1.65436123e-24</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXBAF9CBAE74594B819770B72DA5C1101D">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1.32219867e-07</X>
						<Y>-0.195972815</Y>
						<Z>-0.50000006</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistFrontAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX02B8700334544C5898CE74B7EE5E5E87">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1.32219867e-07</X>
							<Y>-0.199972257</Y>
							<Z>-0.50000006</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXB12FDAB11D6041188E8AC51DB7F4130F">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-1.46413214e-07</X>
						<Y>-0.195972815</Y>
						<Z>0.50000006</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">WaistBackAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXDB12C5BFDB834BA5AD86BA5740CB42BC">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-1.46413214e-07</X>
							<Y>-0.199972257</Y>
							<Z>0.50000006</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Motor6D" referent="RBX8986C15C3A784E2F95CF3E0D1D2752EE">
				<Properties>
					<CoordinateFrame name="C0">
						<X>0</X>
						<Y>-0.342999995</Y>
						<Z>0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-1.1920929e-07</X>
						<Y>-0.195972815</Y>
						<Z>-0</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">Root</string>
					<Ref name="Part0">RBX081D55DA3E944921A37ACF5DF5DC888A</Ref>
					<Ref name="Part1">RBXB8FFBCE768F34D8D8D966F48788528E0</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBXE222EDDB00184F7491BB0D43C949CF64">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>1.99999976</X>
						<Y>0.400055438</Y>
						<Z>1.00000012</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="Humanoid" referent="RBX3EE1D4C9BBC7471C83AC96FD2D21CC45">
			<Properties>
				<bool name="AutoJumpEnabled">true</bool>
				<bool name="AutoRotate">true</bool>
				<bool name="AutomaticScalingEnabled">true</bool>
				<token name="DisplayDistanceType">2</token>
				<float name="HealthDisplayDistance">0</float>
				<token name="HealthDisplayType">0</token>
				<float name="Health_XML">100</float>
				<float name="HipHeight">1.32300007</float>
				<Vector3 name="InternalBodyScale">
					<X>1</X>
					<Y>0.980000019</Y>
					<Z>1</Z>
				</Vector3>
				<float name="InternalHeadScale">1</float>
				<float name="JumpPower">50</float>
				<float name="MaxHealth">100</float>
				<float name="MaxSlopeAngle">89</float>
				<string name="Name">Humanoid</string>
				<float name="NameDisplayDistance">100</float>
				<token name="NameOcclusion">2</token>
				<token name="RigType">1</token>
				<BinaryString name="Tags"></BinaryString>
				<float name="WalkSpeed">16</float>
			</Properties>
			<Item class="NumberValue" referent="RBX72504CBF1A87496E962CE643F5080F1F">
				<Properties>
					<string name="Name">BodyDepthScale</string>
					<BinaryString name="Tags"></BinaryString>
					<double name="Value">1</double>
				</Properties>
			</Item>
			<Item class="NumberValue" referent="RBX1A5F0F81C57248F0B0B59C54896F361E">
				<Properties>
					<string name="Name">BodyHeightScale</string>
					<BinaryString name="Tags"></BinaryString>
					<double name="Value">0.98000001907348632813</double>
				</Properties>
			</Item>
			<Item class="NumberValue" referent="RBX326053CA5D414CB28833B90C6EE0CDF7">
				<Properties>
					<string name="Name">BodyProportionScale</string>
					<BinaryString name="Tags"></BinaryString>
					<double name="Value">0</double>
				</Properties>
			</Item>
			<Item class="NumberValue" referent="RBX31E1FD7086A24899973702702DC72D10">
				<Properties>
					<string name="Name">BodyTypeScale</string>
					<BinaryString name="Tags"></BinaryString>
					<double name="Value">0</double>
				</Properties>
			</Item>
			<Item class="NumberValue" referent="RBX0750B26D31C5425D90FBD73102DD848E">
				<Properties>
					<string name="Name">BodyWidthScale</string>
					<BinaryString name="Tags"></BinaryString>
					<double name="Value">1</double>
				</Properties>
			</Item>
			<Item class="NumberValue" referent="RBX73B9DC33EE6C4AE0A778A6439FFF7378">
				<Properties>
					<string name="Name">HeadScale</string>
					<BinaryString name="Tags"></BinaryString>
					<double name="Value">1</double>
				</Properties>
			</Item>
		</Item>
		<Item class="Part" referent="RBX3A01263AEC0C4FB289F443C3B3EEF144">
			<Properties>
				<bool name="Anchored">false</bool>
				<float name="BackParamA">-0.5</float>
				<float name="BackParamB">0.5</float>
				<token name="BackSurface">0</token>
				<token name="BackSurfaceInput">0</token>
				<float name="BottomParamA">-0.5</float>
				<float name="BottomParamB">0.5</float>
				<token name="BottomSurface">0</token>
				<token name="BottomSurfaceInput">0</token>
				<CoordinateFrame name="CFrame">
					<X>7.66999817</X>
					<Y>4.42000008</Y>
					<Z>-5.99972773</Z>
					<R00>1</R00>
					<R01>0</R01>
					<R02>0</R02>
					<R10>0</R10>
					<R11>1</R11>
					<R12>0</R12>
					<R20>0</R20>
					<R21>0</R21>
					<R22>1</R22>
				</CoordinateFrame>
				<bool name="CanCollide">true</bool>
				<int name="CollisionGroupId">0</int>
				<Color3uint8 name="Color3uint8">4293256415</Color3uint8>
				<PhysicalProperties name="CustomPhysicalProperties">
					<CustomPhysics>false</CustomPhysics>
				</PhysicalProperties>
				<float name="FrontParamA">-0.5</float>
				<float name="FrontParamB">0.5</float>
				<token name="FrontSurface">0</token>
				<token name="FrontSurfaceInput">0</token>
				<float name="LeftParamA">-0.5</float>
				<float name="LeftParamB">0.5</float>
				<token name="LeftSurface">0</token>
				<token name="LeftSurfaceInput">0</token>
				<bool name="Locked">false</bool>
				<token name="Material">256</token>
				<string name="Name">Head</string>
				<float name="Reflectance">0</float>
				<float name="RightParamA">-0.5</float>
				<float name="RightParamB">0.5</float>
				<token name="RightSurface">0</token>
				<token name="RightSurfaceInput">0</token>
				<Vector3 name="RotVelocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<BinaryString name="Tags"></BinaryString>
				<float name="TopParamA">-0.5</float>
				<float name="TopParamB">0.5</float>
				<token name="TopSurface">0</token>
				<token name="TopSurfaceInput">0</token>
				<float name="Transparency">0</float>
				<Vector3 name="Velocity">
					<X>0</X>
					<Y>0</Y>
					<Z>0</Z>
				</Vector3>
				<token name="formFactorRaw">1</token>
				<token name="shape">1</token>
				<Vector3 name="size">
					<X>2</X>
					<Y>1</Y>
					<Z>1</Z>
				</Vector3>
			</Properties>
			<Item class="SpecialMesh" referent="RBX067F1390E0BE4FB19BEF5A28AE77B361">
				<Properties>
					<token name="LODX">2</token>
					<token name="LODY">2</token>
					<Content name="MeshId"><null></null></Content>
					<token name="MeshType">0</token>
					<string name="Name">Mesh</string>
					<Vector3 name="Offset">
						<X>0</X>
						<Y>0</Y>
						<Z>0</Z>
					</Vector3>
					<Vector3 name="Scale">
						<X>1.25</X>
						<Y>1.25</Y>
						<Z>1.25</Z>
					</Vector3>
					<BinaryString name="Tags"></BinaryString>
					<Content name="TextureId"><null></null></Content>
					<Vector3 name="VertexColor">
						<X>1</X>
						<Y>1</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
				<Item class="Vector3Value" referent="RBX2CB521A32C424B428CBA056F29C8BCF9">
					<Properties>
						<string name="Name">OriginalSize</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>1.25</X>
							<Y>1.25</Y>
							<Z>1.25</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX6ED92ACB6AE741C49B23C1074C2BFDF1">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>3.93568822e-09</X>
						<Y>0</Y>
						<Z>-0.000272244215</Z>
						<R00>1</R00>
						<R01>7.87137555e-09</R01>
						<R02>3.02998127e-15</R02>
						<R10>-7.87137555e-09</R10>
						<R11>1</R11>
						<R12>-4.1444258e-16</R12>
						<R20>-3.02998127e-15</R20>
						<R21>4.14442554e-16</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceCenterAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXAD2B480121D14C10AC1EDCAC749D6601">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>3.93568822e-09</X>
							<Y>0</Y>
							<Z>-0.000272244215</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBXA2BFA88557B14C1F955630DD9C41BC55">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>3.93568866e-09</X>
						<Y>0</Y>
						<Z>-0.600272298</Z>
						<R00>1</R00>
						<R01>7.87137555e-09</R01>
						<R02>3.02998127e-15</R02>
						<R10>-7.87137555e-09</R10>
						<R11>1</R11>
						<R12>-4.1444258e-16</R12>
						<R20>-3.02998127e-15</R20>
						<R21>4.14442554e-16</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">FaceFrontAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX32926700A396408897D5D12716569D58">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>3.93568866e-09</X>
							<Y>0</Y>
							<Z>-0.600272298</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX8482A4B614AD4D59ABDC1080ADA69CE7">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>8.65851391e-09</X>
						<Y>0.599999905</Y>
						<Z>-0.000272244215</Z>
						<R00>1</R00>
						<R01>7.87137555e-09</R01>
						<R02>3.02998127e-15</R02>
						<R10>-7.87137555e-09</R10>
						<R11>1</R11>
						<R12>-4.1444258e-16</R12>
						<R20>-3.02998127e-15</R20>
						<R21>4.14442554e-16</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HairAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX6F5B813A112F4EC2BD6E610AA6A86FDE">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>8.65851391e-09</X>
							<Y>0.599999905</Y>
							<Z>-0.000272244215</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX06B4702B97244539AE63E0A0C6539791">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>8.65851391e-09</X>
						<Y>0.599999905</Y>
						<Z>-0.000272244215</Z>
						<R00>1</R00>
						<R01>7.87137555e-09</R01>
						<R02>3.02998127e-15</R02>
						<R10>-7.87137555e-09</R10>
						<R11>1</R11>
						<R12>-4.1444258e-16</R12>
						<R20>-3.02998127e-15</R20>
						<R21>4.14442554e-16</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">HatAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBX8904AA17114445308A50A8BA92191701">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>8.65851391e-09</X>
							<Y>0.599999905</Y>
							<Z>-0.000272244215</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Attachment" referent="RBX0799983697D843DEBA0BD664AC927893">
				<Properties>
					<CoordinateFrame name="CFrame">
						<X>-0</X>
						<Y>-0.500000119</Y>
						<Z>-0.000272244215</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<string name="Name">NeckRigAttachment</string>
					<BinaryString name="Tags"></BinaryString>
					<bool name="Visible">false</bool>
				</Properties>
				<Item class="Vector3Value" referent="RBXB6DC2AC8E98D42DBA78D0C2E794BC152">
					<Properties>
						<string name="Name">OriginalPosition</string>
						<BinaryString name="Tags"></BinaryString>
						<Vector3 name="Value">
							<X>-0</X>
							<Y>-0.500000119</Y>
							<Z>-0.000272244215</Z>
						</Vector3>
					</Properties>
				</Item>
			</Item>
			<Item class="Decal" referent="RBXB71E9F0E5C07471CBEB9E103F98B5FD3">
				<Properties>
					<Color3 name="Color3">4294967295</Color3>
					<token name="Face">5</token>
					<string name="Name">face</string>
					<BinaryString name="Tags"></BinaryString>
					<Content name="Texture"><url>rbxasset://textures/face.png</url></Content>
					<float name="Transparency">0</float>
				</Properties>
			</Item>
			<Item class="Motor6D" referent="RBXA0674A559D63480B8A4BE71A1BF00324">
				<Properties>
					<CoordinateFrame name="C0">
						<X>-5.96046448e-08</X>
						<Y>0.784016788</Y>
						<Z>1.1920929e-07</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<CoordinateFrame name="C1">
						<X>-0</X>
						<Y>-0.500000119</Y>
						<Z>-0.000272244215</Z>
						<R00>1</R00>
						<R01>0</R01>
						<R02>0</R02>
						<R10>0</R10>
						<R11>1</R11>
						<R12>0</R12>
						<R20>0</R20>
						<R21>0</R21>
						<R22>1</R22>
					</CoordinateFrame>
					<float name="DesiredAngle">0</float>
					<bool name="IsAutoJoint">false</bool>
					<float name="MaxVelocity">0</float>
					<string name="Name">Neck</string>
					<Ref name="Part0">RBXA9943A145CA34B9199D5810AE7AC1AD5</Ref>
					<Ref name="Part1">RBX3A01263AEC0C4FB289F443C3B3EEF144</Ref>
					<BinaryString name="Tags"></BinaryString>
				</Properties>
			</Item>
			<Item class="Vector3Value" referent="RBX46AD0A59994F458AA62C06CF029EF0E1">
				<Properties>
					<string name="Name">OriginalSize</string>
					<BinaryString name="Tags"></BinaryString>
					<Vector3 name="Value">
						<X>2</X>
						<Y>1</Y>
						<Z>1</Z>
					</Vector3>
				</Properties>
			</Item>
		</Item>
		<Item class="LocalScript" referent="RBXF0F8BC510A2F4AF4AF023EADD8460426">
			<Properties>
				<bool name="Disabled">false</bool>
				<Content name="LinkedSource"><null></null></Content>
				<string name="Name">Animate</string>
				<string name="ScriptGuid">{023B546E-F0DD-4EFB-A64F-BC4BC43CBC7B}</string>
				<ProtectedString name="Source"></ProtectedString>
				<BinaryString name="Tags"></BinaryString>
			</Properties>
			<Item class="StringValue" referent="RBXE188072B2E8A45F3923B65A3EE9BD5B4">
				<Properties>
					<string name="Name">cheer</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBXBBBEF22507984F6BBD88AC44A75D7D20">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507770677</url></Content>
						<string name="Name">CheerAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBXD9DFBAD72E154DCDB86F9E684E417E71">
				<Properties>
					<string name="Name">climb</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX6A6F5EE6DBB342D281E6D4E9E60FA586">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507765644</url></Content>
						<string name="Name">ClimbAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX9053E411520444EEB3D92D0A9CAFA5F0">
				<Properties>
					<string name="Name">dance</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX83B7B4DD6C2C4DF4B4815850AA873AD3">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507771019</url></Content>
						<string name="Name">Animation1</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX8E97F88D656D49AFA859BC13BE010E8E">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBXED78747C0C95427BB5EA5F011EF83092">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507771955</url></Content>
						<string name="Name">Animation2</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX36FD8E9666B9417E99014D8380A7179D">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBX3A1BD977BA72406484A684B72B31C81D">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507772104</url></Content>
						<string name="Name">Animation3</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX1CD9842BB02F4B6697EF189CF5983021">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX59296448847B4C37A857FE6D2DD9629E">
				<Properties>
					<string name="Name">dance2</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX11F242485C5849B2A4C7B12C22E24043">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507776043</url></Content>
						<string name="Name">Animation1</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX69BE6B7DDE394B478AA7BACA39DD6F0C">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBX281087FA96E24556A5700BD5717F3CF9">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507776720</url></Content>
						<string name="Name">Animation2</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBXCA9D978497154484B8CECA3311ABC5E7">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBX4E1D7B06CDCE48DEB8CE38E0D94D28AA">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507776879</url></Content>
						<string name="Name">Animation3</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX8392953E02B1403BA0C9C6F4F6B45480">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX06F351F28F2D4D9B990D663EFD1A41BF">
				<Properties>
					<string name="Name">dance3</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBXE2D28FBF66764A8890D619F140F006FE">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507777268</url></Content>
						<string name="Name">Animation1</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBXFF88BFE3E30043B2B28C72D0165EDBB4">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBX2D1E583E94E44672A326A8764F315AD2">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507777451</url></Content>
						<string name="Name">Animation2</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBXDD87704058A442B296C573EDFD24CE3B">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBX20300B2555994061BEC485253896EF13">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507777623</url></Content>
						<string name="Name">Animation3</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBXA09D41439E0D4742BAFD352CF0707ADA">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">10</double>
						</Properties>
					</Item>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX995A0F5E43014B08BD6A1D55BC579649">
				<Properties>
					<string name="Name">fall</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX89D4294596D64B5AA67F9463A3851BC1">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507767968</url></Content>
						<string name="Name">FallAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX9D4470882A824F848E8C3BA6B5642F8B">
				<Properties>
					<string name="Name">idle</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX2C85C2ACB0B1419DA8AFBA6ECE3C3A81">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507766388</url></Content>
						<string name="Name">Animation1</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX335E644CC45F4E34A4E40573E2B0C023">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">9</double>
						</Properties>
					</Item>
				</Item>
				<Item class="Animation" referent="RBX30096662093043EF93E3A8064D9A5E62">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507766666</url></Content>
						<string name="Name">Animation2</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
					<Item class="NumberValue" referent="RBX07465354E1F84434BE4E5C268D5CAED6">
						<Properties>
							<string name="Name">Weight</string>
							<BinaryString name="Tags"></BinaryString>
							<double name="Value">1</double>
						</Properties>
					</Item>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX274F4575C77043E18E4147C907A51E00">
				<Properties>
					<string name="Name">jump</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX625D18DE4EEE46918DB973F1CB0F7AEE">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507765000</url></Content>
						<string name="Name">JumpAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBXC8449D0FC66D40FD8E7DB478301AA80F">
				<Properties>
					<string name="Name">laugh</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX5ADB77028FFA457C90289187226AE9BC">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507770818</url></Content>
						<string name="Name">LaughAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBXDFF2833D5D3D471EBADB993E9F5DC39B">
				<Properties>
					<string name="Name">point</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBXB31F4500FDE343CD8A16B114A1A52B51">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507770453</url></Content>
						<string name="Name">PointAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBXC117A62674A34D2D827CEFF5E6757B55">
				<Properties>
					<string name="Name">run</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX23C5AFA8907C402DB6F10009ABE00ADA">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507767714</url></Content>
						<string name="Name">RunAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX2C9D1FBF1078427DB463E4D2514D89B5">
				<Properties>
					<string name="Name">sit</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX91AD6B0649D14A1E96A02D87F5B2782F">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507768133</url></Content>
						<string name="Name">SitAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX715ABEEB0D2D4B8F9C118FAAA9A49F21">
				<Properties>
					<string name="Name">swim</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX61B97117FB9A423587BF919A5D9A51B8">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507784897</url></Content>
						<string name="Name">Swim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX3DE438C9604940858114E2B5D66F6788">
				<Properties>
					<string name="Name">swimidle</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX63F02B7B59FC47DABB1C919604F730A0">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=481825862</url></Content>
						<string name="Name">SwimIdle</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX66826DED2577497C85EC852A2FF82A38">
				<Properties>
					<string name="Name">toollunge</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX5ED69659A62F43BE805216B1BDC47A9E">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=522638767</url></Content>
						<string name="Name">ToolLungeAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX2A1F438F4CE146D99FF3984A059A16AB">
				<Properties>
					<string name="Name">toolnone</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX575D0F39F7B947898D88EFFF13DC51DE">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507768375</url></Content>
						<string name="Name">ToolNoneAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX0E72C94C60624D35AD3F58670FCA5BDA">
				<Properties>
					<string name="Name">toolslash</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBXF1E0A1DA51A94EC0B0CC8F6B3B1E7539">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=522635514</url></Content>
						<string name="Name">ToolSlashAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBXD62280299F464950BEF4A7E3BED1A78E">
				<Properties>
					<string name="Name">walk</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBXA2AA7EFC5E354313B55CCD0E8F70BD08">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=540798782</url></Content>
						<string name="Name">WalkAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
			<Item class="StringValue" referent="RBX42956449A4974F35A487E29E0292E573">
				<Properties>
					<string name="Name">wave</string>
					<BinaryString name="Tags"></BinaryString>
					<string name="Value"></string>
				</Properties>
				<Item class="Animation" referent="RBX0DE3D693C6D1477CBA0BDD846B6C157A">
					<Properties>
						<Content name="AnimationId"><url>http://www.roblox.com/asset/?id=507770239</url></Content>
						<string name="Name">WaveAnim</string>
						<BinaryString name="Tags"></BinaryString>
					</Properties>
				</Item>
			</Item>
		</Item>
	</Item>
</roblox>