namespace RobloxLauncher;

public class Settings
{
    public string Version { get; set; } = "1.0.0";
    public PortableSettings Portable { get; set; } = new PortableSettings();
    public RobloxSettings Roblox { get; set; } = new RobloxSettings();
    public CacheSettings Cache { get; set; } = new CacheSettings();
    public LauncherSettings Launcher { get; set; } = new LauncherSettings();
    public NetworkSettings Network { get; set; } = new NetworkSettings();
    public LoggingSettings Logging { get; set; } = new LoggingSettings();
}

public class PortableSettings
{
    public bool Enabled { get; set; } = true;
    public string BaseDirectory { get; set; } = ".";
    public string RobloxPath { get; set; } = "Roblox";
    public string CachePath { get; set; } = "Cache";
    public string DataPath { get; set; } = "Data";
    public string ToolsPath { get; set; } = "Tools";
}

public class RobloxSettings
{
    public bool AutoUpdate { get; set; } = false;
    public bool CheckOnStartup { get; set; } = true;
    public bool UpdateNotification { get; set; } = true;
    public bool PreserveCache { get; set; } = true;
    public bool SkipUpdateIfWorking { get; set; } = true;
    public string Version { get; set; } = "";
    public string InstallPath { get; set; } = "Roblox";
    public string UserDataPath { get; set; } = "Cache/UserData";
    public string LogLevel { get; set; } = "Info";
    public string UpdateCheckUrl { get; set; } = "https://clientsettingscdn.roblox.com/v1/client-version/WindowsPlayer";
    public string DownloadUrl { get; set; } = "https://setup.rbxcdn.com/RobloxPlayerLauncher.exe";
}

public class CacheSettings
{
    public bool Enabled { get; set; } = true;
    public int MaxSizeGB { get; set; } = 50;
    public bool AutoCleanup { get; set; } = false;
    public int CleanupIntervalDays { get; set; } = 365;
    public bool CompressionEnabled { get; set; } = true;
    public bool PreloadCommonAssets { get; set; } = true;
    public bool PermanentCaching { get; set; } = true;
    public bool CacheEverything { get; set; } = true;
    public bool AggressiveCaching { get; set; } = true;
    public bool InstantCaching { get; set; } = true;
    public bool BackgroundCaching { get; set; } = true;
    public bool CacheOnFirstAccess { get; set; } = true;
    public bool NeverDeleteCache { get; set; } = true;
}

public class LauncherSettings
{
    public string Theme { get; set; } = "Dark";
    public bool AutoStart { get; set; } = false;
    public bool MinimizeToTray { get; set; } = true;
    public bool CheckUpdatesOnStart { get; set; } = true;
    public bool ShowAdvancedOptions { get; set; } = false;
}

public class NetworkSettings
{
    public bool UseProxy { get; set; } = false;
    public string ProxyAddress { get; set; } = "";
    public int ProxyPort { get; set; } = 0;
    public int TimeoutSeconds { get; set; } = 30;
    public int RetryAttempts { get; set; } = 3;
}

public class LoggingSettings
{
    public bool Enabled { get; set; } = true;
    public string Level { get; set; } = "Info";
    public int MaxLogFiles { get; set; } = 10;
    public int MaxLogSizeMB { get; set; } = 10;
}
