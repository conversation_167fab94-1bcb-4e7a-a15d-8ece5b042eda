@echo off
echo ========================================
echo    TESTING MULTIPLE LAUNCHER STARTUPS
echo ========================================
echo.

for /L %%i in (1,1,5) do (
    echo [%%i/5] Starting launcher...
    start /wait /min Launcher\RobloxLauncher.exe
    timeout /t 2 /nobreak >nul
    taskkill /f /im RobloxLauncher.exe >nul 2>&1
    echo [%%i/5] Launcher closed
    echo.
)

echo ========================================
echo    ALL TESTS COMPLETED
echo ========================================
echo.
echo If no update dialogs appeared, the patch is working correctly!
pause
