﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>

    <AssemblyTitle>Roblox Portable Launcher</AssemblyTitle>
    <AssemblyDescription>Portable Roblox Gaming Environment</AssemblyDescription>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
    <PackageReference Include="System.Net.Http" Version="4.3.4" />
  </ItemGroup>

</Project>